/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  dev: {
    '/hsss-hospital-api-pt/': {
      // 要代理的地址
      // target: 'https://wx.tzzlyy.com',
      // target: 'http://*************:9999',
      target: 'https://www.lawjj.cn:10888',
      // target: 'http://**************:8099',
      // target: 'http://*************9:9999',
      // target: 'http://39.99.241.140/',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
    },
    '/hsss-hospital-api-cflzpt/': {
      // 要代理的地址
      // target: 'https://wx.tzzlyy.com',
      // target: 'http://*************:9999',
      target: 'http://*************9:9999',
      // target: 'http://*************9:9999',
      // target: 'http://39.99.241.140/',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
    },
    '/jws-divisional/': {
      // target: 'https://wx.tzzlyy.com',
      // target: 'http://192.168.10.37:8081', 
      target: 'http://*************:8081',
      // target: 'http://**************:8099',
      // target: 'http://*************:8081',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
    },
    '/wyyx/': {
      // target: 'http://************:28081',
      target: 'http://**************:8099',
      changeOrigin: true,
      pathRewrite: {
        '^': '',
      },
    }
  },
  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^' : '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^' : '' },
    },
  },
};
