# WebSocket 消息调试指南

## 问题描述

收不到 WebSocket 消息，后台发送的消息格式为：
```json
{
  "messageId": null,
  "title": "标题",
  "type": "send",
  "content": "一次内容",
  "timestamp": 1759198766645
}
```

## 解决方案

### 1. 更新消息类型定义

已在 `src/utils/websocket.ts` 中更新 `WebSocketMessage` 接口，支持后台消息格式：

```typescript
export interface WebSocketMessage {
  type: 'new_prescription' | 'prescription_audit' | 'prescription_refund' | 
        'prescription_urgent' | 'system_message' | 'auth' | 'send' | string;
  
  // 原有字段
  patientName?: string;
  prescriptionNo?: string;
  status?: string;
  amount?: number;
  message?: string;
  userId?: string;
  userType?: string;
  
  // 后台新增字段
  messageId?: string | null;
  title?: string;
  content?: string;
  timestamp?: number;
}
```

### 2. 更新消息处理逻辑

在 `src/pages/Prescription/index.tsx` 的 `handleWebSocketMessage` 函数中添加了对 `type: "send"` 的处理：

```typescript
case 'send':
  // 处理后台发送的通用消息格式
  showNotification(
    data.title || '新消息',
    data.content || data.message || '收到新消息',
    'info'
  );
  actionRef.current?.reload();
  break;
```

### 3. 增强日志输出

在 `src/utils/websocket.ts` 中增强了消息接收的日志输出：

```typescript
this.ws.onmessage = (event) => {
  console.log('=== WebSocket 原始消息 ===');
  console.log('event.data:', event.data);
  console.log('event.data 类型:', typeof event.data);
  
  try {
    const data = JSON.parse(event.data);
    console.log('解析后的数据:', data);
    console.log('消息类型:', data.type);
    console.log('========================');
    
    handler(data);
  } catch (error) {
    console.error('解析WebSocket消息失败:', error);
    console.error('原始数据:', event.data);
  }
};
```

## 调试步骤

### 步骤 1: 检查 WebSocket 连接

1. 打开浏览器开发者工具（F12）
2. 切换到 Console 标签
3. 查看是否有 "WebSocket连接已建立" 的日志
4. 检查连接 URL 是否正确：`ws://**************:9999/hsss-hospital-api-cflzpt/websocket?userID=8`

### 步骤 2: 查看原始消息

当后台发送消息时，控制台会输出：

```
=== WebSocket 原始消息 ===
event.data: {"messageId":null,"title":"标题","type":"send","content":"一次内容","timestamp":1759198766645}
event.data 类型: string
解析后的数据: {messageId: null, title: "标题", type: "send", content: "一次内容", timestamp: 1759198766645}
消息类型: send
========================
收到WebSocket消息: {messageId: null, title: "标题", type: "send", content: "一次内容", timestamp: 1759198766645}
```

### 步骤 3: 验证消息处理

如果消息被正确处理，应该会：
1. 在控制台看到 "收到WebSocket消息:" 的日志
2. 在页面右下角看到通知弹窗
3. 听到提示音（如果音频已初始化）

### 步骤 4: 使用测试工具

1. 点击页面上的 "WebSocket测试" 按钮
2. 点击 "连接WebSocket"
3. 在 "预设测试消息" 区域，点击 "测试后台消息格式 (type: send)" 按钮
4. 观察是否收到通知

## 常见问题排查

### 问题 1: 连接不上 WebSocket

**症状**: 控制台显示 "WebSocket连接错误"

**解决方法**:
1. 检查后台 WebSocket 服务是否启动
2. 检查 URL 是否正确：`ws://**************:9999/hsss-hospital-api-cflzpt/websocket?userID=8`
3. 检查网络连接
4. 检查防火墙设置

### 问题 2: 连接成功但收不到消息

**症状**: 连接成功，但没有任何消息日志

**解决方法**:
1. 检查后台是否真的发送了消息
2. 使用浏览器的 Network 标签，筛选 WS（WebSocket），查看消息帧
3. 检查 `onMessage` 处理器是否正确设置

### 问题 3: 收到消息但不显示通知

**症状**: 控制台有消息日志，但没有通知弹窗

**解决方法**:
1. 检查消息的 `type` 字段是否为 `"send"`
2. 检查 `handleWebSocketMessage` 函数中的 switch 语句
3. 检查浏览器通知权限
4. 检查 `showNotification` 函数是否正常工作

### 问题 4: 消息格式不匹配

**症状**: 控制台显示 "解析WebSocket消息失败"

**解决方法**:
1. 检查后台发送的是否是有效的 JSON 字符串
2. 查看 "原始数据:" 日志，确认数据格式
3. 如果不是 JSON，需要修改解析逻辑

## 测试用例

### 测试用例 1: 后台消息格式

**输入**:
```json
{
  "messageId": null,
  "title": "测试标题",
  "type": "send",
  "content": "测试内容",
  "timestamp": 1759198766645
}
```

**期望输出**:
- 通知标题: "测试标题"
- 通知内容: "测试内容"
- 通知类型: info（蓝色）
- 播放提示音

### 测试用例 2: 缺少 title 字段

**输入**:
```json
{
  "messageId": null,
  "type": "send",
  "content": "测试内容",
  "timestamp": 1759198766645
}
```

**期望输出**:
- 通知标题: "新消息"（默认值）
- 通知内容: "测试内容"

### 测试用例 3: 缺少 content 字段

**输入**:
```json
{
  "messageId": null,
  "title": "测试标题",
  "type": "send",
  "timestamp": 1759198766645
}
```

**期望输出**:
- 通知标题: "测试标题"
- 通知内容: "收到新消息"（默认值）

## 浏览器 Network 面板调试

### 查看 WebSocket 消息帧

1. 打开开发者工具（F12）
2. 切换到 Network 标签
3. 筛选 WS（WebSocket）
4. 点击 WebSocket 连接
5. 切换到 Messages 标签
6. 查看发送和接收的消息

**示例**:
```
↓ Received (接收)
{"messageId":null,"title":"标题","type":"send","content":"一次内容","timestamp":1759198766645}

↑ Sent (发送)
{"type":"auth","userId":"8","userType":"prescription_manager"}
```

## 相关文件

- `src/utils/websocket.ts` - WebSocket 工具类
- `src/pages/Prescription/index.tsx` - 消息处理逻辑
- `src/pages/Prescription/components/WebSocketTest.tsx` - 测试工具
- `src/utils/audioNotification.ts` - 音频通知

## 联系支持

如果问题仍未解决，请提供以下信息：
1. 完整的控制台日志
2. Network 面板的 WebSocket 消息截图
3. 后台发送的原始消息格式
4. 浏览器版本和操作系统

