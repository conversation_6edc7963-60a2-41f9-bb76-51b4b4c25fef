# WebSocket消息通知修复说明

## 问题描述
处方页面的 `initWebSocket` 方法收到WebSocket消息后弹不出提示。

## 问题分析

经过代码分析，发现了以下几个关键问题：

### 1. 用户ID初始化时机问题
- **问题**: `useEffect` 的依赖数组为空 `[]`，但 `initWebSocket` 依赖于 `initialState?.currentUser?.userID`
- **影响**: 组件首次渲染时，`initialState` 可能还未加载完成，导致 `userId` 为 `undefined`，WebSocket连接失败
- **修复**: 将WebSocket初始化分离到单独的 `useEffect` 中，依赖于 `initialState?.currentUser?.userID`

### 2. WebSocket消息处理器设置时机问题
- **问题**: 在 `PrescriptionWebSocket` 类中，`onMessage` 方法检查 `if (this.ws)`，但调用时 `this.ws` 可能为 `null`
- **影响**: 消息处理器可能没有正确设置，导致收到消息时无法触发处理函数
- **修复**: 
  - 添加 `messageHandler` 私有属性存储处理器函数
  - 在 `connect()` 方法中设置消息处理器
  - 修改 `onMessage()` 方法逻辑

## 修复内容

### 1. 处方页面 (`src/pages/Prescription/index.tsx`)

#### 修复用户ID检查和日志增强
```typescript
// 获取用户ID，如果没有则不初始化WebSocket
const userId = initialState?.currentUser?.userID;
if (!userId) {
  console.warn('用户ID不存在，跳过WebSocket初始化');
  return;
}
```

#### 分离WebSocket初始化逻辑
```typescript
// 单独的useEffect来处理WebSocket初始化，依赖于initialState
useEffect(() => {
  if (initialState?.currentUser?.userID) {
    console.log('用户信息已加载，开始初始化WebSocket');
    initWebSocket();
  }
}, [initialState?.currentUser?.userID]);
```

#### 增强调试日志
- 在 `handleWebSocketMessage` 中添加详细的日志输出
- 在 `showNotification` 中添加通知发送和音频播放的日志
- 添加测试通知功能和按钮

### 2. WebSocket工具类 (`src/utils/websocket.ts`)

#### 添加消息处理器存储
```typescript
private messageHandler: ((message: WebSocketMessage) => void) | null = null;
```

#### 修复消息处理器设置逻辑
```typescript
onMessage(handler: (message: WebSocketMessage) => void): void {
  this.messageHandler = handler;
  
  // 如果WebSocket已经连接，立即设置消息处理器
  if (this.ws && this.ws.readyState === WebSocket.OPEN) {
    // 设置消息处理器
  }
}
```

#### 在连接时设置消息处理器
```typescript
// 在 connect() 方法中
if (this.messageHandler) {
  this.ws.onmessage = (event) => {
    // 处理消息逻辑
    this.messageHandler!(data);
  };
}
```

## 测试功能

### 1. 通知测试按钮
在开发环境下，处方页面工具栏中添加了"测试通知"按钮，可以手动测试通知功能是否正常。

### 2. WebSocket测试组件
已有的WebSocket测试组件可以用来测试WebSocket连接和消息发送。

## 验证步骤

1. **检查控制台日志**:
   - 查看是否有"用户信息已加载，开始初始化WebSocket"日志
   - 查看是否有"WebSocket连接成功"日志
   - 查看是否有"音频通知初始化成功"日志

2. **测试通知功能**:
   - 点击"测试通知"按钮，检查是否弹出通知
   - 检查是否播放提醒声音

3. **测试WebSocket消息**:
   - 使用WebSocket测试组件发送测试消息
   - 检查控制台是否有消息接收日志
   - 检查是否弹出相应的通知

## 注意事项

1. **音频权限**: 浏览器可能需要用户交互后才能播放音频，首次使用时可能需要手动点击测试按钮
2. **WebSocket连接**: 确保WebSocket服务器正常运行且可访问
3. **用户认证**: 确保用户已正确登录且 `initialState.currentUser.userID` 存在

## 后续优化建议

1. 添加WebSocket连接状态显示
2. 添加音频播放权限检查和提示
3. 考虑添加消息历史记录功能
4. 优化错误处理和用户提示
