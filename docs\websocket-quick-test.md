# WebSocket 快速测试指南

## 🚀 快速开始

### 1. 启动应用
```bash
npm run dev
```

访问: http://localhost:8001

### 2. 打开 WebSocket 测试工具

1. 登录系统
2. 进入 "处方管理" 页面
3. 点击页面上的 "WebSocket测试" 按钮

### 3. 连接 WebSocket

点击 "连接WebSocket" 按钮

**期望结果**:
- 按钮变为禁用状态
- 状态显示为 "已连接"
- 控制台输出: `连接WebSocket: ws://**************:9999/hsss-hospital-api-cflzpt/websocket?userID=8`
- 控制台输出: `WebSocket已连接: ...`

### 4. 测试后台消息格式

点击 "测试后台消息格式 (type: send)" 按钮

**期望结果**:
- 控制台输出发送的消息
- 页面右下角弹出通知
- 播放提示音

## 📋 测试检查清单

### ✅ 连接测试
- [ ] WebSocket 连接成功
- [ ] URL 包含 `?userID=8` 参数
- [ ] 控制台无错误信息

### ✅ 消息接收测试
- [ ] 控制台显示 "=== WebSocket 原始消息 ==="
- [ ] 控制台显示解析后的数据
- [ ] 控制台显示消息类型

### ✅ 通知显示测试
- [ ] 页面右下角显示通知弹窗
- [ ] 通知标题正确显示
- [ ] 通知内容正确显示

### ✅ 音频播放测试
- [ ] 收到消息时播放提示音
- [ ] 可以单独测试音频播放

## 🔍 控制台日志示例

### 成功连接
```
连接WebSocket: ws://**************:9999/hsss-hospital-api-cflzpt/websocket?userID=8
WebSocket已连接: ws://**************:9999/hsss-hospital-api-cflzpt/websocket?userID=8
WebSocket连接已建立
```

### 收到消息
```
=== WebSocket 原始消息 ===
event.data: {"messageId":null,"title":"后台消息标题","type":"send","content":"这是后台发送的消息内容","timestamp":1759198766645}
event.data 类型: string
解析后的数据: {messageId: null, title: "后台消息标题", type: "send", content: "这是后台发送的消息内容", timestamp: 1759198766645}
消息类型: send
========================
收到WebSocket消息: {messageId: null, title: "后台消息标题", type: "send", content: "这是后台发送的消息内容", timestamp: 1759198766645}
```

## 🧪 测试场景

### 场景 1: 测试后台消息格式

**步骤**:
1. 连接 WebSocket
2. 点击 "测试后台消息格式 (type: send)"

**期望**:
- 收到通知: 标题 "后台消息标题"，内容 "这是后台发送的消息内容..."

### 场景 2: 自定义消息内容

**步骤**:
1. 连接 WebSocket
2. 在 "消息类型" 下拉框选择 "后台消息格式 (send)"
3. 在 "消息内容" 文本框输入自定义内容
4. 点击 "发送测试消息"

**期望**:
- 收到通知，内容为自定义的文本

### 场景 3: 测试其他消息类型

**步骤**:
1. 连接 WebSocket
2. 点击预设消息按钮（新处方通知、审核通过等）

**期望**:
- 每种消息类型都能正确显示通知

### 场景 4: 音频测试

**步骤**:
1. 点击 "测试音频播放" 按钮

**期望**:
- 播放提示音
- 控制台显示 "音频播放测试成功"

### 场景 5: 组合测试

**步骤**:
1. 点击 "信息通知+音频" 按钮

**期望**:
- 显示通知
- 播放提示音

## 🐛 常见问题

### 问题: 连接失败

**检查**:
1. 后台 WebSocket 服务是否启动
2. URL 是否正确
3. 网络是否通畅

**解决**:
```bash
# 检查后台服务
curl http://**************:9999/health

# 检查端口是否开放
telnet ************** 9999
```

### 问题: 收不到消息

**检查**:
1. 打开 Network 面板，筛选 WS
2. 查看 Messages 标签
3. 确认后台是否发送了消息

**解决**:
- 查看控制台日志
- 检查消息格式是否正确
- 确认 `type` 字段为 `"send"`

### 问题: 没有通知弹窗

**检查**:
1. 浏览器通知权限
2. 控制台是否有错误
3. `showNotification` 函数是否正常

**解决**:
- 允许浏览器通知权限
- 检查代码逻辑
- 查看错误日志

### 问题: 没有提示音

**检查**:
1. 浏览器音量设置
2. 音频文件是否存在
3. 音频权限

**解决**:
- 调整音量
- 检查 `/public/audio/` 目录
- 点击 "测试音频播放" 按钮

## 📊 测试数据

### 后台消息格式示例

```json
{
  "messageId": null,
  "title": "处方审核通知",
  "type": "send",
  "content": "您的处方 CF202312250001 已通过审核",
  "timestamp": 1759198766645
}
```

```json
{
  "messageId": "msg_001",
  "title": "紧急通知",
  "type": "send",
  "content": "有新的紧急处方需要处理",
  "timestamp": 1759198766645
}
```

```json
{
  "messageId": null,
  "title": "系统维护通知",
  "type": "send",
  "content": "系统将于今晚 22:00 进行维护，预计持续 2 小时",
  "timestamp": 1759198766645
}
```

## 🔧 调试技巧

### 1. 使用浏览器 Network 面板

1. F12 打开开发者工具
2. Network 标签
3. 筛选 WS
4. 查看 Messages

### 2. 使用 Console 日志

所有 WebSocket 消息都会在控制台输出详细日志，包括：
- 原始数据
- 解析后的数据
- 消息类型

### 3. 使用测试工具

WebSocket 测试工具提供了完整的测试功能：
- 连接/断开
- 发送自定义消息
- 发送预设消息
- 音频测试
- 通知测试

## 📝 测试报告模板

```
测试时间: 2025-09-30 14:30:00
测试人员: [姓名]
测试环境: Chrome 120 / Windows 11

测试结果:
✅ WebSocket 连接成功
✅ 消息接收正常
✅ 通知显示正常
✅ 音频播放正常
❌ [问题描述]

问题详情:
[详细描述问题]

控制台日志:
[粘贴相关日志]

截图:
[附上截图]
```

## 🎯 下一步

如果所有测试都通过，可以：
1. 让后台发送真实消息进行测试
2. 测试不同的消息类型
3. 测试长时间连接的稳定性
4. 测试断线重连功能
5. 测试并发消息处理

## 📞 获取帮助

如果遇到问题，请：
1. 查看 `docs/websocket-message-debug.md` 详细调试指南
2. 检查控制台日志
3. 查看 Network 面板的 WebSocket 消息
4. 提供完整的错误信息和日志

