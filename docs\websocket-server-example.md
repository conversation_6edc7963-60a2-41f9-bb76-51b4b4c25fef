# WebSocket服务器配置示例

## 概述

本文档提供了处方管理系统WebSocket通知功能的后端服务器配置示例。

## Node.js + Express + ws 示例

### 1. 安装依赖

```bash
npm install express ws cors
npm install -D @types/ws @types/express
```

### 2. 服务器代码示例

```javascript
const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const cors = require('cors');

const app = express();
const server = http.createServer(app);

// 启用CORS
app.use(cors());
app.use(express.json());

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server,
  path: '/prescription-notifications'
});

// 存储连接的客户端
const clients = new Map();

// WebSocket连接处理
wss.on('connection', (ws, req) => {
  console.log('新的WebSocket连接');
  
  let clientId = null;
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('收到消息:', data);
      
      // 处理身份验证
      if (data.type === 'auth') {
        clientId = data.userId;
        clients.set(clientId, {
          ws,
          userType: data.userType,
          connectedAt: new Date()
        });
        console.log(`用户 ${clientId} 已认证，类型: ${data.userType}`);
        
        // 发送认证成功消息
        ws.send(JSON.stringify({
          type: 'auth_success',
          message: '认证成功'
        }));
      }
      
      // 处理心跳
      if (data.message === 'ping') {
        ws.send(JSON.stringify({
          type: 'system_message',
          message: 'pong'
        }));
      }
    } catch (error) {
      console.error('解析消息失败:', error);
    }
  });
  
  ws.on('close', () => {
    if (clientId) {
      clients.delete(clientId);
      console.log(`用户 ${clientId} 断开连接`);
    }
  });
  
  ws.on('error', (error) => {
    console.error('WebSocket错误:', error);
  });
});

// 广播消息给所有连接的客户端
function broadcastMessage(message) {
  clients.forEach((client, clientId) => {
    if (client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        console.log(`消息已发送给用户 ${clientId}`);
      } catch (error) {
        console.error(`发送消息给用户 ${clientId} 失败:`, error);
      }
    }
  });
}

// 发送消息给特定用户类型
function sendToUserType(userType, message) {
  clients.forEach((client, clientId) => {
    if (client.userType === userType && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        console.log(`消息已发送给 ${userType} 用户 ${clientId}`);
      } catch (error) {
        console.error(`发送消息给用户 ${clientId} 失败:`, error);
      }
    }
  });
}

// REST API 端点用于触发通知
app.post('/api/notify/new-prescription', (req, res) => {
  const { patientName, prescriptionNo } = req.body;
  
  const message = {
    type: 'new_prescription',
    patientName,
    prescriptionNo,
    timestamp: new Date().toISOString()
  };
  
  sendToUserType('prescription_manager', message);
  
  res.json({ success: true, message: '新处方通知已发送' });
});

app.post('/api/notify/prescription-audit', (req, res) => {
  const { prescriptionNo, status } = req.body;
  
  const message = {
    type: 'prescription_audit',
    prescriptionNo,
    status,
    timestamp: new Date().toISOString()
  };
  
  sendToUserType('prescription_manager', message);
  
  res.json({ success: true, message: '处方审核通知已发送' });
});

app.post('/api/notify/prescription-refund', (req, res) => {
  const { prescriptionNo, amount } = req.body;
  
  const message = {
    type: 'prescription_refund',
    prescriptionNo,
    amount,
    timestamp: new Date().toISOString()
  };
  
  sendToUserType('prescription_manager', message);
  
  res.json({ success: true, message: '处方退费通知已发送' });
});

app.post('/api/notify/urgent-prescription', (req, res) => {
  const { patientName, prescriptionNo } = req.body;
  
  const message = {
    type: 'prescription_urgent',
    patientName,
    prescriptionNo,
    timestamp: new Date().toISOString()
  };
  
  sendToUserType('prescription_manager', message);
  
  res.json({ success: true, message: '紧急处方通知已发送' });
});

app.post('/api/notify/system-message', (req, res) => {
  const { message: systemMessage } = req.body;
  
  const message = {
    type: 'system_message',
    message: systemMessage,
    timestamp: new Date().toISOString()
  };
  
  broadcastMessage(message);
  
  res.json({ success: true, message: '系统消息已发送' });
});

// 获取连接状态
app.get('/api/websocket/status', (req, res) => {
  const status = {
    totalConnections: clients.size,
    clients: Array.from(clients.entries()).map(([clientId, client]) => ({
      clientId,
      userType: client.userType,
      connectedAt: client.connectedAt
    }))
  };
  
  res.json(status);
});

// 启动服务器
const PORT = process.env.PORT || 8080;
server.listen(PORT, () => {
  console.log(`WebSocket服务器运行在端口 ${PORT}`);
  console.log(`WebSocket端点: ws://localhost:${PORT}/prescription-notifications`);
});
```

### 3. 测试API调用示例

```bash
# 发送新处方通知
curl -X POST http://localhost:8080/api/notify/new-prescription \
  -H "Content-Type: application/json" \
  -d '{"patientName": "张三", "prescriptionNo": "CF202312250001"}'

# 发送审核通知
curl -X POST http://localhost:8080/api/notify/prescription-audit \
  -H "Content-Type: application/json" \
  -d '{"prescriptionNo": "CF202312250001", "status": "审核通过"}'

# 发送退费通知
curl -X POST http://localhost:8080/api/notify/prescription-refund \
  -H "Content-Type: application/json" \
  -d '{"prescriptionNo": "CF202312250001", "amount": 158.5}'

# 发送紧急处方通知
curl -X POST http://localhost:8080/api/notify/urgent-prescription \
  -H "Content-Type: application/json" \
  -d '{"patientName": "李四", "prescriptionNo": "CF202312250002"}'

# 发送系统消息
curl -X POST http://localhost:8080/api/notify/system-message \
  -H "Content-Type: application/json" \
  -d '{"message": "系统将在30分钟后进行维护"}'

# 查看连接状态
curl http://localhost:8080/api/websocket/status
```

## 消息格式说明

### 客户端发送的消息格式

```typescript
// 身份验证
{
  type: 'auth',
  userId: 'user123',
  userType: 'prescription_manager'
}

// 心跳
{
  type: 'system_message',
  message: 'ping'
}
```

### 服务端发送的消息格式

```typescript
// 新处方通知
{
  type: 'new_prescription',
  patientName: '张三',
  prescriptionNo: 'CF202312250001',
  timestamp: '2023-12-25T10:30:00.000Z'
}

// 处方审核通知
{
  type: 'prescription_audit',
  prescriptionNo: 'CF202312250001',
  status: '审核通过',
  timestamp: '2023-12-25T10:35:00.000Z'
}

// 处方退费通知
{
  type: 'prescription_refund',
  prescriptionNo: 'CF202312250001',
  amount: 158.5,
  timestamp: '2023-12-25T10:40:00.000Z'
}

// 紧急处方通知
{
  type: 'prescription_urgent',
  patientName: '李四',
  prescriptionNo: 'CF202312250002',
  timestamp: '2023-12-25T10:45:00.000Z'
}

// 系统消息
{
  type: 'system_message',
  message: '系统将在30分钟后进行维护',
  timestamp: '2023-12-25T10:50:00.000Z'
}
```

## 部署说明

1. 确保服务器支持WebSocket协议
2. 配置反向代理（如Nginx）支持WebSocket升级
3. 设置适当的防火墙规则
4. 考虑使用SSL/TLS加密（wss://）
5. 实现适当的身份验证和授权机制

## 注意事项

1. 生产环境中应该实现更严格的身份验证
2. 考虑使用Redis等存储来支持多实例部署
3. 实现消息持久化以防止消息丢失
4. 添加速率限制以防止滥用
5. 监控WebSocket连接数量和性能指标
