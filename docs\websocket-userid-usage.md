# WebSocket URL 中传入 userID 参数

## 功能说明

WebSocket 连接现在支持在 URL 中传入 `userID` 参数，用于标识连接的用户身份。

## 使用方法

### 1. 基本用法

```typescript
import { getWebSocketUrl } from '@/utils/websocket';

// 传入用户ID
const wsUrl = getWebSocketUrl(8);
// 结果: ws://**************:9999/websocket?userID=8

// 不传参数（不带userID）
const wsUrl = getWebSocketUrl();
// 结果: ws://**************:9999/websocket
```

### 2. 在 Prescription 页面中的使用

```typescript
const initWebSocket = async () => {
  const { PrescriptionWebSocket, getWebSocketUrl } = await import('@/utils/websocket');
  
  // 获取用户ID，如果没有则使用默认值8
  const userId = initialState?.currentUser?.id || '8';
  
  const wsConfig = {
    url: getWebSocketUrl(userId),  // 传入用户ID
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
  };
  
  wsRef.current = new PrescriptionWebSocket(wsConfig);
  await wsRef.current.connect();
};
```

### 3. 在 WebSocketTest 组件中的使用

```typescript
const connectWebSocket = async () => {
  const { getWebSocketUrl } = await import('@/utils/websocket');
  
  // 测试时使用固定的 userID=8
  const wsUrl = getWebSocketUrl(8);
  
  wsRef.current = new WebSocket(wsUrl);
};
```

## API 说明

### getWebSocketUrl(userId?)

生成 WebSocket 连接 URL。

**参数:**
- `userId` (可选): `string | number` - 用户ID

**返回值:**
- `string` - 完整的 WebSocket URL

**示例:**

```typescript
// 开发环境
getWebSocketUrl(8)
// => ws://**************:9999/websocket?userID=8

getWebSocketUrl('user123')
// => ws://**************:9999/websocket?userID=user123

getWebSocketUrl()
// => ws://**************:9999/websocket

// 生产环境 (HTTPS)
getWebSocketUrl(8)
// => wss://yourdomain.com/websocket?userID=8
```

## 测试方法

### 1. 使用 WebSocket 测试工具

1. 访问处方管理页面
2. 点击"WebSocket测试"按钮
3. 点击"连接WebSocket"按钮
4. 查看控制台输出，确认连接 URL 包含 `?userID=8`

### 2. 查看控制台日志

连接成功后，控制台会输出：
```
连接WebSocket: ws://**************:9999/websocket?userID=8
WebSocket已连接: ws://**************:9999/websocket?userID=8
```

### 3. 服务端验证

服务端可以通过解析 URL 参数获取 userID：

```javascript
// Node.js 示例
const url = require('url');

wss.on('connection', (ws, req) => {
  const params = url.parse(req.url, true).query;
  const userId = params.userID;
  
  console.log('用户连接:', userId);
  // 可以将 ws 与 userId 关联存储
});
```

## 注意事项

1. **默认值**: 如果没有传入 `userId`，URL 将不包含 `userID` 参数
2. **类型支持**: `userId` 支持 `string` 和 `number` 类型
3. **URL 编码**: 如果 `userId` 包含特殊字符，会自动进行 URL 编码
4. **环境区分**: 
   - 开发环境: `ws://**************:9999/websocket`
   - 生产环境: 根据当前协议自动选择 `ws://` 或 `wss://`

## 相关文件

- `src/utils/websocket.ts` - WebSocket 工具类
- `src/pages/Prescription/index.tsx` - 处方管理页面
- `src/pages/Prescription/components/WebSocketTest.tsx` - WebSocket 测试组件

