/**
 * WebSocket 使用示例 - 带 userID 参数
 */

import { PrescriptionWebSocket, getWebSocketUrl } from '@/utils/websocket';

// ============================================
// 示例 1: 基本使用 - 传入用户ID
// ============================================
export const example1_BasicUsageWithUserId = async () => {
  // 生成带 userID 的 WebSocket URL
  const wsUrl = getWebSocketUrl(8);
  console.log('WebSocket URL:', wsUrl);
  // 输出: ws://**************:9999/websocket?userID=8

  // 创建 WebSocket 连接
  const ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    console.log('WebSocket 连接成功');
  };

  ws.onmessage = (event) => {
    console.log('收到消息:', event.data);
  };

  ws.onerror = (error) => {
    console.error('WebSocket 错误:', error);
  };

  ws.onclose = () => {
    console.log('WebSocket 连接关闭');
  };
};

// ============================================
// 示例 2: 使用 PrescriptionWebSocket 类
// ============================================
export const example2_UsePrescriptionWebSocket = async () => {
  const userId = 8;

  const wsConfig = {
    url: getWebSocketUrl(userId),
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
  };

  const ws = new PrescriptionWebSocket(wsConfig);

  // 设置消息处理器
  ws.onMessage((data) => {
    console.log('收到消息:', data);
    
    switch (data.type) {
      case 'new_prescription':
        console.log('新处方:', data.prescriptionNo);
        break;
      case 'prescription_audit':
        console.log('处方审核:', data.status);
        break;
      default:
        console.log('其他消息:', data);
    }
  });

  // 连接
  await ws.connect();

  // 发送消息
  ws.send({
    type: 'auth',
    userId: String(userId),
    userType: 'prescription_manager'
  });
};

// ============================================
// 示例 3: 动态用户ID（从登录状态获取）
// ============================================
export const example3_DynamicUserId = async (currentUser: any) => {
  // 从当前用户获取 ID，如果没有则使用默认值
  const userId = currentUser?.id || '8';

  const wsConfig = {
    url: getWebSocketUrl(userId),
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    heartbeatInterval: 30000,
  };

  const ws = new PrescriptionWebSocket(wsConfig);

  ws.onMessage((data) => {
    console.log(`用户 ${userId} 收到消息:`, data);
  });

  await ws.connect();
};

// ============================================
// 示例 4: 不带 userID 参数
// ============================================
export const example4_WithoutUserId = async () => {
  // 不传入 userId，URL 将不包含 userID 参数
  const wsUrl = getWebSocketUrl();
  console.log('WebSocket URL:', wsUrl);
  // 输出: ws://**************:9999/websocket

  const ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    console.log('WebSocket 连接成功（无 userID）');
  };
};

// ============================================
// 示例 5: 字符串类型的 userID
// ============================================
export const example5_StringUserId = async () => {
  // 使用字符串类型的 userID
  const wsUrl = getWebSocketUrl('user_abc_123');
  console.log('WebSocket URL:', wsUrl);
  // 输出: ws://**************:9999/websocket?userID=user_abc_123

  const ws = new WebSocket(wsUrl);

  ws.onopen = () => {
    console.log('WebSocket 连接成功（字符串 userID）');
  };
};

// ============================================
// 示例 6: 在 React 组件中使用
// ============================================
export const example6_InReactComponent = () => {
  // 这是一个伪代码示例，展示在 React 组件中的使用方式
  
  /*
  import { useEffect, useRef } from 'react';
  import { useModel } from '@umijs/max';
  import { PrescriptionWebSocket, getWebSocketUrl } from '@/utils/websocket';

  const MyComponent = () => {
    const { initialState } = useModel('@@initialState');
    const wsRef = useRef<PrescriptionWebSocket | null>(null);

    useEffect(() => {
      const initWebSocket = async () => {
        const userId = initialState?.currentUser?.id || '8';
        
        const wsConfig = {
          url: getWebSocketUrl(userId),
          reconnectInterval: 5000,
          maxReconnectAttempts: 10,
          heartbeatInterval: 30000,
        };

        wsRef.current = new PrescriptionWebSocket(wsConfig);
        
        wsRef.current.onMessage((data) => {
          console.log('收到消息:', data);
          // 处理消息...
        });

        await wsRef.current.connect();
      };

      initWebSocket();

      return () => {
        if (wsRef.current) {
          wsRef.current.close();
        }
      };
    }, [initialState]);

    return <div>WebSocket Component</div>;
  };
  */
};

// ============================================
// 示例 7: 测试不同环境的 URL
// ============================================
export const example7_TestDifferentEnvironments = () => {
  console.log('=== WebSocket URL 测试 ===');
  
  // 开发环境
  console.log('开发环境 (带 userID):', getWebSocketUrl(8));
  // ws://**************:9999/websocket?userID=8
  
  console.log('开发环境 (不带 userID):', getWebSocketUrl());
  // ws://**************:9999/websocket
  
  // 生产环境会根据当前协议自动选择 ws:// 或 wss://
  // 如果是 HTTPS，则使用 wss://
  // 如果是 HTTP，则使用 ws://
};

// ============================================
// 使用说明
// ============================================
/*
1. 基本用法:
   const wsUrl = getWebSocketUrl(8);
   const ws = new WebSocket(wsUrl);

2. 使用 PrescriptionWebSocket 类:
   const ws = new PrescriptionWebSocket({ url: getWebSocketUrl(8) });
   await ws.connect();

3. 动态获取用户ID:
   const userId = currentUser?.id || '8';
   const wsUrl = getWebSocketUrl(userId);

4. 不带 userID:
   const wsUrl = getWebSocketUrl();

5. 字符串类型的 userID:
   const wsUrl = getWebSocketUrl('user123');
*/

