{"name": "ant-design-pro", "version": "6.0.0-beta.1", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "openapi": "max openapi", "playwright": "playwright install && playwright test", "prepare": "husky install", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test:e2e": "node ./tests/run-tests.js", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.7.0", "@ant-design/pro-components": "^2.6.43", "@umijs/route-utils": "^2.1.3", "@xkit-yx/call-kit": "^3.3.2", "@xkit-yx/call-kit-react-ui": "^0.10.0", "ahooks": "^3.7.3", "antd": "^4.23.3", "classnames": "^2.3.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dingrtc": "^3.6.2", "echarts": "^5.4.3", "html2canvas": "^1.4.1", "js-md5": "^0.7.3", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.4", "nim-web-sdk-ng": "^10.8.1", "omit.js": "^2.0.2", "rc-menu": "^9.6.4", "rc-util": "^5.24.4", "react": "^17.0.0", "react-barcode": "^1.6.1", "react-dev-inspector": "^1.8.1", "react-dom": "^17.0.0", "react-flip-numbers": "^3.0.8", "react-helmet-async": "^1.3.0", "react-to-print": "^3.1.1"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@babel/plugin-proposal-decorators": "^7.20.5", "@playwright/test": "^1.26.1", "@types/classnames": "^2.3.1", "@types/express": "^4.17.14", "@types/history": "^4.7.11", "@types/lodash": "^4.14.186", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.5", "@umijs/fabric": "^2.11.1", "@umijs/max": "^4.0.24", "@umijs/openapi": "^1.7.0", "cross-env": "^7.0.3", "cross-port-killer": "^1.4.0", "detect-installer": "^1.0.2", "eslint": "^7.32.0", "gh-pages": "^3.2.0", "husky": "^7.0.4", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "prettier": "^2.7.1", "swagger-ui-dist": "^4.14.2", "typescript": "^4.8.4", "umi-presets-pro": "^1.0.5", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}