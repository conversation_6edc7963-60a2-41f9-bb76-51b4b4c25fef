import { DefaultFooter } from '@ant-design/pro-components';
import { getServerFilesHost } from '@/utils/index'
import '@umijs/max';
const Footer: React.FC = () => {
  const defaultMessage = '杭州京威盛智能科技有限公司';
  const currentYear = new Date().getFullYear();
  return (
    <DefaultFooter
      style={{
        background: 'none',
      }}
      copyright={`2011-${currentYear} ${defaultMessage}`}
      links={[
        {
          key: 'title',
          title: '相关下载：',
          href: '',
          blankTarget: false,
        },
        {
          key: '32位浏览器',
          title: '32位浏览器',
          href: `${getServerFilesHost()}google/109.0.5414.75_chrome_installer_x86.exe`,
          blankTarget: true,
        },
        {
          key: '64位浏览器',
          title: '64位浏览器',
          href: `${getServerFilesHost()}google/124.0.6367.119_chrome_installer-win64.exe`,
          blankTarget: true,
        },
      ]}
    />
  );
};
export default Footer;
