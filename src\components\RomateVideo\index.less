@import (reference) '~antd/es/style/themes/index';
.video-content{
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    .wrapper{
        position: relative;
        width: 100%;
        height: 100%;
    }
}
.media-wrapper{
    position: absolute;
    top: 5px;
    right: 100px;
    width: 400px;
    height: 710px;
    z-index: 1000;
    .handle{
        background-color: #fff;
        padding: 5px;
        border: 1px solid;
        cursor: 'move';
        font-size: 16px;
    }
    .content{
        position: relative;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        .my{
            position: absolute;
            top: 20px;
            right: 30px;
            width: 200px;
            height: 160px; 
        }
        
        .romate-user{
            width: 100%;
            height: 100%;
            border: 1px solid red;
            text-align: center;
            font-size: 18px;
            color: #fff;
            .loading-text{
                margin-top: 50px;
            }
        }

        .close-btn{
            position: absolute;
            bottom: 40px;
            left: 50%;
            margin-left: -35px;
            width: 70px;
            height: 70px;   
            line-height: 70px;
            background: red;
            border-radius: 50%;
            font-size: 40px;
            color: #fff;
            text-align: center;
            cursor: pointer;
            .anticon{
                transform: rotate(225deg);
            }
        }

        .botton-wrapper{
            width: 100%;
            display: flex;
            position: absolute;
            bottom: 40px;
            flex-direction: row;
            justify-content: space-around;
            .closeBtn{
                width: 70px;
                height: 70px;   
                line-height: 70px;
                background: red;
                border-radius: 50%;
                font-size: 40px;
                color: #fff;
                text-align: center;
                cursor: pointer;
                .anticon{
                    transform: rotate(225deg);
                }
            }

            .openBtn{
                width: 70px;
                height: 70px;   
                line-height: 70px;
                background: green;
                border-radius: 50%;
                font-size: 40px;
                color: #fff;
                text-align: center;
                cursor: pointer;
            }
        }


    }

}





