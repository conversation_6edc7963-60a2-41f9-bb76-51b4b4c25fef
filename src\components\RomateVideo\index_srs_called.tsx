import React, {useRef, useState, useImper<PERSON><PERSON><PERSON><PERSON>, forwardRef, useEffect} from 'react'
import { <PERSON><PERSON>, Modal, Drawer, Select, Image, message} from 'antd';
import Draggable from 'react-draggable'
import { PhoneFilled } from '@ant-design/icons'
import { createRoom, editRoom, getRoomStatusByRoomId} from '@/services/api/audio';
import { SrsRtcWhipWhepAsync} from '@/utils/srs.sdk.js'
import { encrypt } from '@/utils'
import '@umijs/max';
import './index.less'

export type tokenType = {
    userID?: string;
    channelID?: string;
    token?: string
    userName?: string;
    status?: number;
    state?: number;
    roomId?: string;
};

/**
 * @zh-CN 请求视频通话token
 * @param fields
 */
const handleQueryGenerateToken = async (fields: tokenType) => {
  try {
    const response = await createRoom({ ...fields });
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    message.error('获取音视频通话token失败');
    return false;
  }
};

/**
 * @zh-CN 请求频道状态
 * @param fields
 */
const handleGetChannelStatusByChannel = async (fields: tokenType) => {
    try {
      const response = await getRoomStatusByRoomId({ ...fields });
      if (response.code === '0') {
        return response.data;
      }
      message.error(response.msg);
      return false;
    } catch (error) {
      message.error('网络异常');
      return false;
    }
};

/**
 * @zh-CN 修改频道状态
 * @param fields
 */
const handleEditChannel = async (fields: tokenType) => {
    try {
      const response = await editRoom({ ...fields });
      if (response.code === '0') {
        return response.data;
      }
      message.error(response.msg);
      return false;
    } catch (error) {
      message.error('网络异常');
      return false;
    }
};

const RomateVideo = forwardRef((props: any, ref) => {
    const [myUserID, setMyUserID] = useState<string>('')
    const [userID, setUserID] = useState<string>('');
    const [state, setStateCode] = useState<number>(0)  // 0 初始化 1 正在呼叫 2 对话中 3 结束通话
    // 创建客户端实例
    const sigRef = useRef(null)
    const publishRef = useRef(null)
    const playerRef = useRef(null)
    const audioRingRef = useRef(null)
    const audioNoResRef = useRef(null)
    const audioRejectRef = useRef(null)
    const timeOutRef = useRef(null)
    const timeInterval = useRef(null)

    const [publisherStream, setPublisherStream] = useState<MediaStream | null>(null);
    const [playerStream, setPlayerStream] = useState<MediaStream | null>(null);
    const publisherVideoRef = useRef<HTMLVideoElement>(null);
    const playerVideoRef = useRef<HTMLVideoElement>(null);

    const handleEditStatus = async (code: number, user: string) => {
        console.log(user, code)
        const res = await handleEditChannel({
            state: code,
            roomId: encrypt(user)
        })
    }

    const close = (type?: 'active' | 'overtime' | 'reject' ) => {
        if(timeOutRef.current){
            clearTimeout(timeOutRef.current);
        }
        if(timeInterval.current){
            clearInterval(timeInterval.current);
        }
        if(type === 'active' || type === 'overtime'){
            handleEditStatus(4, userID);
        }
        if(playerRef.current){
            playerRef.current.close();
        }
        if(publishRef.current){
            publishRef.current.close();
        }
        if(audioRingRef.current){
            audioRingRef.current.pause();
        }
        if(type === 'overtime'){
            audioNoResRef.current.play();
        }
        setStateCode(3)
        props.setIsCalled()
        props.setDeviceCode(null)
    }

    const open = () => {
        // 接听
        handleEditStatus(2, userID);
        if(timeOutRef.current){
            clearTimeout(timeOutRef.current);
        }
        if(audioRingRef.current){
            audioRingRef.current.pause();
        }
    }

    const startPlay = async function (url) {
        const player = SrsRtcWhipWhepAsync();
        playerRef.current = player;
        await player.play(url);
        setPlayerStream(player.stream);
    };

    const startPublish = async function (url) {
        const publisher = SrsRtcWhipWhepAsync()
        publishRef.current = publisher;
        await publisher.publish(url);
        setPublisherStream(publisher.stream);
    };
    
    const handleQueryPeerStatus = async () => {
        const res = await handleGetChannelStatusByChannel({
            // userID: encrypt(userID),
            roomId: encrypt(userID)
        })
        if(res && res.state === 3) {
            // 判断对方已离开房间 关闭
            message.info('对方已挂断！')
            close('reject')
        }else if(res && res.state === 2){
            // 如果信令ok 开始推流
            if(state === 1){
                if(timeOutRef.current){
                    clearTimeout(timeOutRef.current);
                }
                if(audioRingRef.current){
                    audioRingRef.current.pause();
                }
                setStateCode(2)
                await startPublish(res.playersUrlJson.whip[0].url); // 开始推流
                await startPlay(res.playersUrlJson.whep[1].url);
            }
        }else if(res && res.state === 4){
            // 我方拒绝 挂断
            close('active')
        }
    }

    // 唤起界面
    const apply = async (prop: tokenType) => {
        setMyUserID(prop.userName);
        setUserID(prop.channelID); // 呼叫的对象即为当前的房间号
        handleEditStatus(0, prop.channelID);  // 状态置为0 等待接听
        audioRingRef.current.play();
        setStateCode(1)
    }

    useEffect(() => {
        if (publisherStream && publisherVideoRef.current) {
          publisherVideoRef.current.srcObject = publisherStream;
        }
      }, [publisherStream]);
    
      useEffect(() => {
        if (playerStream && playerVideoRef.current) {
          playerVideoRef.current.srcObject = playerStream;
        }
      }, [playerStream]);

    useEffect(()=>{
        if(state === 1){
            timeOutRef.current = setTimeout(()=>{
                close('overtime')
            }, 60000)
            timeInterval.current = setInterval(()=>{
                handleQueryPeerStatus()
            }, 1000)
        }else if(state === 2){
            timeInterval.current = setInterval(()=>{
                handleQueryPeerStatus()
            }, 1000)
        }
        return ()=> {
            clearTimeout(timeOutRef.current)
            clearInterval(timeInterval.current)
        }
    }, [state])
    
    useImperativeHandle(ref, () => ({ apply }));
    
    return (
        <>
            <div className='video-content'>
                <div className='wrapper' />
            </div>
            { (state === 1 || state === 2) && <Draggable handle=".handle" bounds=".wrapper">
                <div className="media-wrapper">
                    <div className="handle">视频通话</div>
                    <div className='content'>
                        <div className='my' id="player">
                            <video id="rtc_media_publisher" width="200" autoPlay muted ref={publisherVideoRef}></video>
                        </div>
                        <div className="romate-user" id={`uid${myUserID}`}>
                            { state === 1 && <div className="loading-text">{myUserID}</div> }
                            <video id="rtc_media_player" width="399" autoPlay ref={playerVideoRef}></video>
                        </div>

                        { state === 1 ?  <div className='botton-wrapper'>
                            <div className='closeBtn' onClick={() => close('active')}>
                                <PhoneFilled />
                            </div>
                            <div className='openBtn' onClick={() => open()}>
                                <PhoneFilled />
                            </div>
                        </div> :  <div className='close-btn' onClick={() => close('active')}>
                            <PhoneFilled />
                        </div>
                        }
                    </div>
                </div>
            </Draggable>}
            <audio ref={audioRingRef} src={`audio/avchat_ring.mp3`} loop hidden>
                Your browser does not support the audio element.
            </audio>
            <audio ref={audioNoResRef} src={`audio/avchat_no_response.mp3`} hidden>
                Your browser does not support the audio element.
            </audio>
            <audio ref={audioRejectRef} src={`audio/avchat_peer_reject.mp3`} hidden>
                Your browser does not support the audio element.
            </audio>
        </>
    );
});

export default RomateVideo;