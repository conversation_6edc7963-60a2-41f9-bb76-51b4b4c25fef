// 医院管理相关查询接口 处理SELECT中的查询数据
import {
  useState,
  useCallback
} from 'react';
import { getDigDeptDoctorList } from '@/services/api/queue';
import {
  queryHosList,
  queryDeptList,
  queryDocList,
  queryClinicList,
  queryDiagAreaListNoPage,
  getDeptInfo,
  getDiagAreaInfo,
  getDoctorInfo,
  queryDiagnosisList,
  queryDescribesList,
  queryUsagesList,
  queryFrequencyList
} from '@/services/api/hospital';
import {
  message
} from 'antd'

const MAX_LENGTH = 999;
export type ListType = {
  value?: any;
  label?: string;
};

export default () => {
  const [docList, setDocList] = useState<ListType[]>([]);
  const [hosList, setHosList] = useState<ListType[]>([]);
  const [deptList, setDeptList] = useState<ListType[]>([]);
  const [clinicList, setClinicList] = useState<ListType[]>([]);
  const [digAreaList, setDigAreaList] = useState<ListType[]>([]);
  const [deptStatus, setDeptStatus] = useState<string>();
  const [doctorStatus, setDoctorStatus] = useState<string>();
  const [diagStatus, setDiagStatus] = useState<string>();
  const [deptListByID, setDeptListByID] = useState<ListType[]>();
  const [deptListByDig, setDeptListByDig] = useState<ListType[]>([]);
  const [diagnosisList, setDiagnosisList] = useState<ListType[]>([]);
  const [describesList, setDescribesList] = useState<ListType[]>([]);
  const [frequencyList, setFrequencyList] = useState<ListType[]>([]);
  const [usagesList, setUsagesList] = useState<ListType[]>([]);

  // 查询机构列表方法
  const fetchHosList = useCallback(async () => {
    const result = await queryHosList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return { label: el.hospitalName, value: el.saID}
      });
      console.log(data)
      setHosList(data)
      return data
    } else {
      message.error('请求机构信息失败,请刷新重试');
      return false
    }
  }, []);
  
  // 查询诊室列表方法
  const fetchClinicList = useCallback(async () => {
    const result = await queryClinicList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data =  result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.roomName,
          value: el.roomID.toString(),
        }
      });
      setClinicList(data)
      return data
    } else {
      message.error('请求诊室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询科室列表方法
  const fetchDeptList = useCallback(async () => {
    const result = await queryDeptList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.deptName,
          value: el.deptCode,
        };
      });
      setDeptList(data)
      return data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

    // 查询医生列表方法
  const fetchDocList = useCallback(async (params) => {
    const result = await queryDocList({
      ...params,
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.doctorName,
          value: `${el.doctorName}-${el.doctorCode}-${el.deptCode}`,
        };
      });
      setDocList(data)
      return data
    } else {
      message.error('请求查询医生信息失败,请刷新重试');
      return false
    }
  }, []);

    // 查询诊区内的科室
  const fetchDeptListByDig = useCallback(async (digAreaCode) => {
    const result = await getDigDeptDoctorList({
      digAreaCode
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.deptName,
          value: el.deptCode,
        };
      });
      console.log(data)
      setDeptListByDig(data)
      return data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询科室ID列表方法
  const fetchDeptListByID = useCallback(async () => {
    const result = await queryDeptList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.deptName,
          value: el.deptID,
        };
      });
      setDeptListByID(data)
      return data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询诊区列表方法
  const fetchDigAreaList = useCallback(async () => {
    const result = await queryDiagAreaListNoPage({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.digAreaName,
          value: el.digID,
        };
      });
      setDigAreaList(data)
      return data
    } else {
      message.error('请求诊区信息失败,请刷新重试');
      return false
    }
  }, []);

   // 查询科室信息
  const fetchDeptStatus = useCallback(async (deptID) => {
    const result = await getDeptInfo({
      deptID
    });
    if (result.code === '0' && result.data) {
      setDeptStatus(result.data)
      return result.data
    } else {
      message.error('请求科室信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询医生信息
  const fetchDoctorStatus = useCallback(async(params) => {
    const result = await getDoctorInfo({
      ...params
    });
    if (result.code === '0' && result.data) {
      setDoctorStatus(result.data)
      return result.data
    } else {
      message.error('请求医生信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询诊区信息
  const fetchDiagStatus = useCallback(async (digAreaCode) => {
    const result = await getDiagAreaInfo({
      digAreaCode
    });
    if (result.code === '0' && result.data) {
      setDiagStatus(result.data)
      return result.data
    } else {
      message.error('请求诊区信息失败,请刷新重试');
      return false
    }
  }, []);


  // 查询诊断信息
  const fetchDiagnosisList = useCallback(async () => {
    const result = await queryDiagnosisList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      setDiagnosisList(result.data)
      return result.data
    } else {
      message.error('请求诊断信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询病情描述
  const fetchDescribesList = useCallback(async () => {
    const result = await queryDescribesList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      setDescribesList(result.data)
      return result.data
    } else {
      message.error('请求病情描述信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询频次列表方法
  const fetchFrequencyList = useCallback(async () => {
    const result = await queryFrequencyList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.name,
          value: el.code,
        };
      });
      setFrequencyList(data)
      return data
    } else {
      message.error('请求药品频次信息失败,请刷新重试');
      return false
    }
  }, []);

  // 查询用法信息
  const fetchUsageList = useCallback(async () => {
    const result = await queryUsagesList({
      current: 1,
      pageSize: MAX_LENGTH,
    });
    if (result.code === '0' && result.data) {
      const data = result.data.map((el: {[key: string]: any }) => {
        return {
          label: el.name,
          value: el.code,
        };
      });
      setUsagesList(data)
      return data
    } else {
      message.error('请求药品用法信息失败,请刷新重试');
      return false
    }
  }, []);
  
  return {
    hosList,
    docList,
    deptList,
    clinicList,
    digAreaList,
    deptStatus,
    diagStatus,
    doctorStatus,
    deptListByID,
    deptListByDig,
    frequencyList,
    usagesList,
    fetchDocList,
    fetchHosList,
    fetchClinicList,
    fetchDeptList,
    fetchDigAreaList,
    fetchDeptStatus,
    fetchDiagStatus,
    fetchDoctorStatus,
    fetchDeptListByID,
    fetchDeptListByDig,
    fetchFrequencyList,
    fetchUsageList
  };
};
