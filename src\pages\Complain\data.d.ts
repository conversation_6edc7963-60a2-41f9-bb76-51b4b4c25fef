declare namespace COMPLAIN {
  // 投诉列表项
  type ComplaintListItem = {
    id?: number;
    name?: string;
    patientName?: string;
    phone?: string;
    content?: string;
    complaint?: string;
    createTime?: string;
    saID?: string;
    hospitalName?: string;
    status?: number; // 0-未处理 1-已处理 2-已忽略
    handleTime?: string;
    handleUser?: string;
    handleRemark?: string;
    ID?: string;
  };

  // 投诉推送绑定列表项
  type ComplaintPushListItem = {
    id?: number;
    receiverName?: string;
    receiverEmail?: string;
    receiverPhone?: string;
    saID?: string;
    hospitalName?: string;
    pushType?: number; // 1-邮件推送 2-短信推送 3-微信推送
    status?: number; // 0-禁用 1-启用
    createTime?: string;
    remark?: string;
    ID?: string;
  };

  // API响应类型
  type ApiResponse<T = any> = {
    code: string;
    msg?: string;
    data?: T;
  };

  // 分页参数
  type PageParams = {
    current?: number;
    pageSize?: number;
    [key: string]: any;
  };

  // 分页响应
  type PageResponse<T = any> = {
    data?: T[];
    total?: number;
    success?: boolean;
  };
}
