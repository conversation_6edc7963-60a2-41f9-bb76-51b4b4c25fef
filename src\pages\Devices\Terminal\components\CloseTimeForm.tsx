import {
  ModalForm,
  ProFormTextArea,
  ProFormSelect,
  ProFormTimePicker,
  ProFormDependency
} from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};


type FormValueType = Partial<DEVICES.CmdTypeItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values?: any;
};

const CloseTimeForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit, values}) => {

  return (
    <ModalForm
      title="定时关机"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={(fields) => {
        const data = { 
          cmdContent: fields.cmdContent, // 更新内容
          optType: fields.optType, // 命令类型 4 版本更新
        };
        return onSubmit(data);
      }}
      {...formItemLayout}
      initialValues={{ deviceCodes: values?.selectedRowsState?.map(item => item.deviceCode).join('，')}}
      className="_modal-wrapper"
    >
      <ProFormTextArea name="deviceCodes" label="设备编号" fieldProps={{ rows: 2 }} readonly/>
      <ProFormSelect
        name="optType"
        label="操作"
        initialValue={'11'}
        rules={[{ required: true, message: '操作为必填项' }]}
        valueEnum={{
          11: '设置关机时间',
          12: '取消关机时间'
        }}
      />
      <ProFormDependency name={['optType']}>
        {({ optType }) => {
          return optType === '11' && <ProFormTimePicker name="cmdContent" label="关机时间" fieldProps={{
            format: 'HH:mm'
          }}/>
        }}
      </ProFormDependency>
      
    </ModalForm>
  );
};

export default CloseTimeForm;
