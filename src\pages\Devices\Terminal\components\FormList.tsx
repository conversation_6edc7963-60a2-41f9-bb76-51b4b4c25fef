import { ProFormText, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList } = useModel('dictionary');
  const { fetchHosList } = useModel('hospital');
  const { fetchRoleList } = useModel('system');
  return (
    <>
      <ProFormText hidden name="deviceID" label="设备ID" />
      <ProFormText
        rules={[
          {
            required: true,
            message: '终端编号为必填项',
          },
        ]}
        name="deviceCode"
        label="终端编号"
        placeholder="请输入终端编号"
      />
      <ProFormText name="hisDeviceCode" label="HIS终端编号" />
      <ProFormText
        rules={[
          {
            required: true,
            message: '终端名称为必填项',
          },
        ]}
        name="deviceName"
        label="终端名称"
        placeholder="请输入终端名称"
      />
      <ProFormSelect
        name="saID"
        label="关联机构"
        rules={[
          {
            required: true,
            message: '终端名称为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchHosList()}
        showSearch
      />
      <ProFormText name="deviceAddress" label="设备位置" placeholder="请输入设备位置" />
      <ProFormText name="deviceIP" label="IP地址" placeholder="请输入IP地址" />
      <ProFormSelect
        name="deviceType"
        label="设备类型"
        rules={[
          {
            required: true,
            message: '设备类型为必填项',
          },
        ]}
        placeholder="请选择设备类型"
        request={() => fetchDicList('deviceType', 'number')}
      />
      <ProFormSelect
        name="roleID"
        label="设备角色"
        rules={[
          {
            required: true,
            message: '设备角色为必填项',
          },
        ]}
        request={() => fetchRoleList()}
        placeholder="请选择设备类型"
      />
      <ProFormSelect
        name="sysType"
        label={'系统类型'}
        placeholder="请选择设备类型"
        request={() => fetchDicList('sysType', 'number')}
      />
      <ProFormText name="remark" placeholder="请输入备注" label="备注" />
      <ProFormText name="moduleID" hidden />
    </>
  );
};

export default FormList;
