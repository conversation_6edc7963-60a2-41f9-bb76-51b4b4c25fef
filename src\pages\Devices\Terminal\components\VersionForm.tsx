import { useState } from 'react';
import {
  ModalForm,
  ProFormUploadButton,
  ProFormTextArea,
  ProFormSelect,
} from '@ant-design/pro-form';
import { message } from 'antd';
import defaultSettings from '../../../../../config/defaultSettings';
import type { UploadFile } from 'antd/es/upload/interface';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

const UPLOAD_URL = `/${defaultSettings.apiName}/system/uploadFiles.np`;

type FormValueType = Partial<DEVICES.CmdTypeItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const VersionForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit }) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [filePath, setFilePath] = useState<string>('');

  const uploadProps = {
    name: 'multipartFile',
    data: {
      moduleSrc: 'version',
    },
    action: UPLOAD_URL,
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info: any) {
      let proUploadList = [...info.fileList];
      proUploadList = proUploadList.slice(-1);
      setFileList(proUploadList);
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 文件上传成功。`);
        setFilePath(info.file.response.data);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件上传失败。`);
      }
    },
  };

  return (
    <ModalForm
      title="版本更新"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={(values) => {
        const data = { 
          cmdContent: values.cmdContent, // 更新内容
          cmdSrc: filePath, // 命令src
          optType: values.optType, // 命令类型 4 版本更新
        };
        return onSubmit(data);
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormSelect
        name="optType"
        label="软件版本类型"
        initialValue={'4'}
        rules={[{ required: true, message: '软件版本类型为必填项' }]}
        valueEnum={{
          4: '自助机软件',
          6: '第三方软件',
          7: '打印模板',
        }}
      />
      <ProFormTextArea name="versionDesc" label="版本更新说明" fieldProps={{ rows: 5 }} />
      <ProFormUploadButton
        name="file"
        label="版本更新文件"
        fieldProps={uploadProps}
        fileList={fileList}
        max={1}
        rules={[{ required: true, message: '版本更新文件为必填项' }]}
      />
    </ModalForm>
  );
};

export default VersionForm;
