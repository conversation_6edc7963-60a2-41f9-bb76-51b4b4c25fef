import { ProFormText, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList } = useModel('dictionary')
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '诊断名称为必填项',
          },
        ]}
        name="name"
        label="诊断名称"
        placeholder="请输入诊断名称"
      />
      {/* <ProFormSelect 
        rules={[
          {
            required: true,
            message: '诊断类型为必填项',
          },
        ]} 
        name="type" 
        label="诊断类型"
        placeholder="请选择诊断类型" 
        request={() => fetchDicList('DiagnosisType')}
      /> */}
      <ProFormText
        name="icd"
        label="诊断代码"
        placeholder="请输入诊断代码"
      />
      <ProFormText
        name="sort"
        label="诊断序号"
        placeholder="请输入诊断序号"
      />
      <ProFormText
        name="state"
        label="状态"
        hidden
      />
    </>
  );
};

export default FormList;
