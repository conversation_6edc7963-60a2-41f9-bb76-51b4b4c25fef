declare namespace DESCRIBES {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 病情描述
    type DescribesListItem = {        
        isTest?: boolean; // 是否测试数据
        hospitalName?: string;
        saID?: string;      // 机构ID
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        name?: string; // 病情名称
        code?: string;  // 病情代码
        pyCode?: string; //拼音搜索
        sort?: string;  // 病情序号
        type?: string;     // 类型
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number;    // 状态
        medicine?: string;
    };
    // 病情描述列表
    type DescribesList = {
        /** 列表的内容 **/
        data?: DescribesListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}