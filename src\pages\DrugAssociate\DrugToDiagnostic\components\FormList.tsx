import { ProFormText, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList } = useModel('dictionary');
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '药品名称为必填项',
          },
        ]}
        name="medicine"
        label="药品名称"
        placeholder="请输入药品名称"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: '诊断名称为必填项',
          },
        ]}
        name="diagnosis"
        label="诊断名称"
        placeholder="请输入诊断名称"
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: '诊断国标代码为必填项',
          },
        ]}
        name="icd"
        label="诊断国标代码"
        placeholder="请输入诊断国标代码"
      />
    </>
  );
};

export default FormList;
