import { ModalForm,  ProFormDateRangePicker} from '@ant-design/pro-form';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<PRESCRIPTION.ItemListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
};

const RefundForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit}) => {

  return (
    <ModalForm
      title="请选择导入数据处方的时间范围"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        okText: '确认',
        cancelText: '关闭',
      }}
      onFinish={(fields) => {
        return onSubmit({
          ...fields,
        })
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormDateRangePicker
        name="invoice"
        label="日期范围"
        rules={[
          {
            required: true,
            message: '日期范围为必填项',
          },
        ]}
        transform={(value)=>{
          return {
            beginTime: value[0],
            endTime: value[1] 
          }
        }}
      />
    </ModalForm>
  );
};

export default RefundForm;