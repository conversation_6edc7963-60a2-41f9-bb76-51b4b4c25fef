import {
  PlusOutlined,
  InfoCircleOutlined,
  SelectOutlined
} from '@ant-design/icons';
import { But<PERSON>, Modal, Drawer, Select, message} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { getMedicineDiagnosisCorrelation, updateMedicineDiagnosisCorrelation, addMedicineDiagnosisCorrelation, updateCorrelationUsecount} from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import ImportForm from './components/ImportForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加药品关联诊断信息
 * @param fields
 */
const handleAdd = (fields: DIAGNOSISTODRUG.DiagnosisListItem) => handleOperateMethod(addMedicineDiagnosisCorrelation, fields, 'add');

/**
 * @zh-CN 修改药品关联诊断信息
 * @param fields
 */
const handleUpdate = (fields: DIAGNOSISTODRUG.DiagnosisListItem) => handleOperateMethod(updateMedicineDiagnosisCorrelation, fields, 'update');

/**
 * @zh-CN 删除药品关联诊断信息
 * @param deviceID
 */
const handleRemove = (fields: DIAGNOSISTODRUG.DiagnosisListItem) =>
  handleOperateMethod(updateMedicineDiagnosisCorrelation, { id: fields.id, medicine: fields.medicine, diagnosis: fields.diagnosis, state: 9 }, 'delete');

/**
 * @zh-CN 一键导入
 * @param fields
 */
const handleImport = async (fields: any)=>{
  const hide = message.loading('数据查询导入中...');
  try {
    const response = await updateCorrelationUsecount({ ...fields });
    hide()
    if (response.code === '0') {
      message.success('导入成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('数据导入失败请重试！');
    return false;
  }
}

const TableList = () => {
  const { initialState } = useModel('@@initialState');
  // 字典接口调用
  const { fetchDicList, dicList } = useModel('dictionary');
  /** 新增药品关联诊断信息的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改药品关联诊断信息的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 一键导入 */
  const [importModalVisible, handleImportModalVisible] = useState(false);
  /** 展示药品关联诊断详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DIAGNOSISTODRUG.DiagnosisListItem>();

  useEffect(() => {
    fetchDicList('DiagnosisType');
  }, []);
  
  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }

  const columns: ProColumns<DIAGNOSISTODRUG.DiagnosisListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '药品名称',
      dataIndex: 'medicine',
      key: 'medicine',
    },
    {
      title: '诊断名称',
      dataIndex: 'diagnosis',
      key: 'diagnosis',
      hideInSearch: true,
    },
    {
      title: '诊断国标代码',
      dataIndex: 'icd',
      key: 'icd',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 250,
      render: (_, record) => [
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.medicine}这条对应记录?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
          <Button
            type="primary"
            key="upload"
            onClick={() => {
              handleImportModalVisible(true);
            }}
          >
            <SelectOutlined /> 一键导入数据
          </Button>
        ]}
        request={getMedicineDiagnosisCorrelation}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleAdd({...value});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id});

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      {importModalVisible && (
        <ImportForm
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleImport({ ...value});
            // const success = false
            if (success) {
              handleImportModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleImportModalVisible(false);
          }}
          modalVisible={importModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions
            column={1}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<DIAGNOSISTODRUG.DiagnosisListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
