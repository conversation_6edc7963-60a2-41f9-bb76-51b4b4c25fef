declare namespace DIAGNOSISTODRUG {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 诊断项目
    type DiagnosisListItem = {        
        isTest?: boolean; // 是否测试数据
        saID?: string;      // 机构ID
        hospitalName?: string;  // 机构名称
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        name?: string; // 诊断名称
        code?: string;  // 诊断代码
        pyCode?: string; // 诊断拼音
        sort?: string;  // 诊断排序
        type?: string;     // 类型
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number;    // 状态
        medicine?: string;
        diagnosis?: string;
    };
    // 诊断列表
    type DiagnosisList = {
        /** 列表的内容 **/
        data?: DiagnosisListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}