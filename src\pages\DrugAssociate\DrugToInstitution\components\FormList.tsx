import { ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchHosList } = useModel('hospital');
  return (
    <>
      <ProFormSelect
        rules={[
          {
            required: true,
            message: '机构名称为必填项',
          },
        ]}
        name="saID"
        label="机构名称"
        request={() => fetchHosList()}
        placeholder="请选择机构名称"
        showSearch
      />
      <ProFormSelect
        rules={[
          {
            required: true,
            message: '药品库主机构为必填项',
          },
        ]}
        name="fatherID"
        label="主机构"
        request={async () => { const res = await fetchHosList(); return res.filter(c => c.value[0] !== 'P')}}
        placeholder="请选择药品库主机构"
        showSearch
      />
    </>
  );
};

export default FormList;
