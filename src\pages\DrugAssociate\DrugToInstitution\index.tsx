import {
  PlusOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { But<PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { getMedicineSystemAssociationList, addMedicineSystemAssociation, updateMedicineSystemAssociation } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import { handleOperateMethod } from '@/utils/index';

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加机构药品库关联信息
 * @param fields
 */
const handleAdd = (fields: DRUGTOINSTITUTION.ListItem) => handleOperateMethod(addMedicineSystemAssociation, fields, 'add');

/**
 * @zh-CN 修改机构药品库关联信息
 * @param fields
 */
const handleUpdate = (fields: DRUGTOINSTITUTION.ListItem) => handleOperateMethod(updateMedicineSystemAssociation, fields, 'update');

/**
 * @zh-CN 删除机构药品库关联信息
 * @param deviceID
 */
const handleRemove = (id?: string) =>
  handleOperateMethod(updateMedicineSystemAssociation, { id, state: 9 }, 'delete');

const TableList = () => {
  /** 新增药品关联诊断信息的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改药品关联诊断信息的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示药品关联诊断详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DRUGTOINSTITUTION.ListItem>();
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);
  
  const columns: ProColumns<DRUGTOINSTITUTION.ListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select {...rest} placeholder="请选择" showSearch optionFilterProp="label" options={options} />
        );
      },
    },
    {
      title: '药品库主机构',
      dataIndex: 'fatherID',
      key: 'fatherID',
      renderText: (_, record) => {
        return record.fatherName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.filter(c => c.value[0] !== 'P').map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select {...rest} placeholder="请选择" showSearch optionFilterProp="label" options={options} />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 250,
      render: (_, record) => [
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.hospitalName}的关联关系?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.id);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>
        ]}
        request={getMedicineSystemAssociationList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleAdd({...value});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id});

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions
            column={1}
            title={currentRow?.hospitalName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<DRUGTOINSTITUTION.ListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
