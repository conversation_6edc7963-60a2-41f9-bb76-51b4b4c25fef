declare namespace DRUGTOINSTITUTION {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 诊断项目
    type ListItem = {        
        isTest?: boolean; // 是否测试数据
        id?: string;  // 记录id
        saID?: string;      // 机构ID
        hospitalName?: string;// 机构名称
        fatherID?: string;    // 关联主机构
        fatherName?: string;     // 主机构名称
        state?: number;    // 状态
    };
    // 诊断列表
    type InsititutionList = {
        /** 列表的内容 **/
        data?: ListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}