import { ModalForm } from '@ant-design/pro-form';
import FormList from './FormList';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<DESCRIBES.DescribesListItem>;

type CreateFormProps = {
	onCancel: (flag?: boolean, formVals?: FormValueType) => void;
	onSubmit: (values: FormValueType) => Promise<void>;
	modalVisible: boolean;
};

const CreateForm: React.FC<CreateFormProps> = ({ modalVisible, onCancel, onSubmit }) => {
  return (
    <ModalForm
      title="新增频次信息"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <FormList />
    </ModalForm>
  );
};

export default CreateForm;