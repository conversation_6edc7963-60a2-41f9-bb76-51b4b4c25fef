import { ProFormText } from '@ant-design/pro-form';

const FormList = ({type}) => {
  return (
    <>
      <ProFormText rules={[
        {
          required: true,
          message: '机构名称为必填项',
        },
      ]} name="saID" label="机构ID" hidden = { type === 'edit'}/>
      <ProFormText
        rules={[
          {
            required: true,
            message: '机构名称为必填项',
          },
        ]}
        name="hospitalName"
        label="机构名称"
        placeholder="请输入机构名称"
      />
      <ProFormText 
        rules={[
          {
            required: true,
            message: '机构代码为必填项',
          },
        ]} 
        name="systemKey" 
        label="机构代码"
        placeholder="请输入机构代码"
      />
      {/* <ProFormText
        rules={[
          {
            required: true,
            message: '系统名称为必填项',
          },
        ]}
        name="systemName"
        label="系统名称"
        placeholder="请输入系统名称"
      /> */}
      <ProFormText name="contactName" label="机构联系人" placeholder="请输入机构联系人" />
      <ProFormText name="contactTel" label="联系方式" placeholder="请输入联系方式" />
      {/* <ProFormText name="iconUrl" label="医院logo" placeholder="请输入医院logo" />
      <ProFormText name="hisUrl" label="HIS接口地址" placeholder="请输入HIS接口地址" />
      <ProFormText name="payUrl" label="支付接口地址" placeholder="请输入支付接口地址" /> */}
    </>
  );
};

export default FormList;
