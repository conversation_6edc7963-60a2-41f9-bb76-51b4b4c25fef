import { useState } from 'react';
import { ProFormText, ProFormSelect, ProFormTextArea, ProFormUploadButton} from '@ant-design/pro-form';
import { ProFormDependency } from '@ant-design/pro-components';
import {message, Upload} from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { useModel } from '@umijs/max';
import defaultSettings from '../../../../../config/defaultSettings';


const UPLOAD_URL = `/${defaultSettings.apiName}/system/uploadDoctorFiles.np`


const FormList = () => {
  const { fetchDeptList, fetchHosList} = useModel('hospital');
  const [ fileList, setFileList ] = useState<UploadFile[]>([])
  const [ filePath, setFilePath ] = useState<string>('')
  return (
    <>
      <ProFormSelect
        name="saID"
        label="关联机构"
        rules={[
          {
            required: true,
            message: '终端名称为必填项',
          },
        ]}
        colProps={{ span: 12 }}
        request={() => fetchHosList()}
        showSearch
      />
      <ProFormText
        rules={[
          {
            required: true,
            message: '药师姓名为必填项',
          },
        ]}
        name="doctorName"
        label="药师姓名"
        placeholder="请输入医生姓名"
      />
      <ProFormText
        name="doctorCode" 
        label="药师代码"
        placeholder="请输入药师代码"
      />
      <ProFormText name="title" label="药师职称" placeholder="请输入药师职称" />
      <ProFormDependency name={['saID', 'doctorName']}>
        {({ saID, doctorName}) => {
          return (
            <ProFormUploadButton 
              name="icon" 
              label="药师签名图" 
              fieldProps={{
                name: 'multipartFile',
                listType: 'picture-card',
                data: {
                  moduleSrc: "doctorsign",
                  uploadType: 1,
                  doctorCode: saID,
                  doctorName: doctorName
                },
                action: UPLOAD_URL,
                headers: {
                  authorization: 'authorization-text',
                },
                beforeUpload: (file) => {
                  const isJpgOrPng = file.type === 'image/png';
                  const isSize = file.size / 1024;
                  if (!saID) {
                    message.error('上传药师签名图片请先选择关联机构');
                    return Upload.LIST_IGNORE
                  }
                  if (!doctorName) {
                    message.error('上传药师签名图片请先输入药师姓名');
                    return Upload.LIST_IGNORE
                  }
                  if (!isJpgOrPng) {
                    message.error('只能提交PNG格式文件');
                    return Upload.LIST_IGNORE
                  }
                  // if (isSize > 100) {
                  //   message.error('图片大小不可超过100KB')
                  //   return Upload.LIST_IGNORE
                  // }
                  return true;
                },
                onChange(info: any) {
                  console.log(info)
                  let proUploadList = [...info.fileList];
                  proUploadList = proUploadList.slice(-1);
                  // setFileList(proUploadList)
                  if (info.file.status !== 'uploading') {
                    console.log(info.file, info.fileList);
                  }
                  if (info.file.status === 'done') {
                    message.success(`${info.file.name} 文件上传成功。`);
                    setFilePath(info.file.response.data)
                  } else if (info.file.status === 'error') {
                    message.error(`${info.file.name} 文件上传失败。`);
                  }
                },
              }}
              max={1}
            />
          );
        }}
      </ProFormDependency> 
    </>
  );
};

export default FormList;
