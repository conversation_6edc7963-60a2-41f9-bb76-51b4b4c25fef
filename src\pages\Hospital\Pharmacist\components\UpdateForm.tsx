import { ModalForm } from '@ant-design/pro-form';
import FormList from './FormList';
import 'moment/locale/zh-cn';
import { getServerFilesHost } from '../../../../utils';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<DOCTOR.DoctorListItem>;

export type UpdateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  updateModalVisible: boolean;
  values: FormValueType;
};

const UpdateForm: React.FC<UpdateFormProps> = ({ updateModalVisible, values, onCancel, onSubmit }) => {
  const DEFAULT_IMAGE = values.icon ? [{
    uid: '-1',
    name: values.icon,
    status: 'done',
    url: getServerFilesHost() + values.icon,
  }] : []
  return (
    <ModalForm
      title="修改医生"
      layout="horizontal"
      width={640}
      open={updateModalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      initialValues={{ ...values, icon: DEFAULT_IMAGE}}
      className="_modal-wrapper"
    >
      <FormList />
    </ModalForm>
  );
};

export default UpdateForm;
