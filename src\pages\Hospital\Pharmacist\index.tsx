import {
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { <PERSON><PERSON>, Modal, Drawer, Select} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryDocList, updateDoc, addDoc } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import SettingsForm from './components/SettingsForm';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加医生
 * @param fields
 */
const handleAdd = (fields: DOCTOR.DoctorListItem) => handleOperateMethod(addDoc, fields, 'add');
/**
 * @zh-CN 修改医生
 * @param fields
 */
const handleUpdate = (fields: DOCTOR.DoctorListItem) => handleOperateMethod(updateDoc, fields, 'update');
/**
 * @zh-CN 删除医生
 * @param deviceID
 */
const handleRemove = (docID?: number) =>
  handleOperateMethod(updateDoc, { docID, state: 9 }, 'delete');

const TableList = () => {
  /** 新增医生的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改医生的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示医生详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<DOCTOR.DoctorListItem>();
  /** 医生设置的弹窗 */
  const [settingsModalVisible, handleSettingsModalVisible] = useState(false);
  const { hosList, fetchHosList, deptList, fetchDeptList} = useModel('hospital');

  useEffect(() => {
    fetchHosList();
    fetchDeptList();
  }, []);

  const columns: ProColumns<DOCTOR.DoctorListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }, form) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`${c.value}`}>
                    {c.label}
                  </Option>
                );
              })}
          </Select>
        );
      },
    },
    {
      title: '药师姓名',
      dataIndex: 'doctorName',
      key: 'doctorName',
    },
    {
      title: '药师代码',
      dataIndex: 'doctorCode',
      key: 'doctorCode',
    },
    {
      title: '药师职称',
      dataIndex: 'title',
      key: 'title',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 250,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.doctorName}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.docID);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        request={queryDocList}
        columns={columns}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleAdd({...value, icon: value.icon ? value.icon[0].response.data : null});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            console.log(value)
            let icon = null;
            if(value.icon && value.icon.length > 0){
              icon = value.icon[0].response?.data || value.icon[0].name
            }
            const success = await handleUpdate({ ...value, docID: currentRow?.docID, icon});

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.doctorName && (
          <ProDescriptions
            column={1}
            title={currentRow?.doctorName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.docID,
            }}
            columns={columns as ProDescriptionsItemProps<DOCTOR.DoctorListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
