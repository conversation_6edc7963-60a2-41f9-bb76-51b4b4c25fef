import { queryPayList, queryPay} from '@/services/api/logs';
import { BarsOutlined} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import '@umijs/max';
import { Button, message, Select, Divider} from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { parseParam } from '@/utils';
import { typeObjectFormat } from './utils';
import { useModel } from '@umijs/max';
import OrderDetail from './components/OrderDetails';
import defaultSettings from '../../../../config/defaultSettings';

const { Option } = Select;
export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 刷新订单
 * @param fields
 */
const handleQueryPay = async (fields: PAYS.PayListItem) => {
  try {
    const response = await queryPay({ ...fields });
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    message.error('刷新订单信息失败！');
    return false;
  }
};

/**
 * 导出Excel
 * @param fields
 */

const handleExportToExcel = async fields => {
  const hide = message.loading('正在导出Excel');

  try {
    const paramsStr = parseParam(fields);
    const aLink = document.createElement('a');
    document.body.appendChild(aLink);
    aLink.style.display = 'none';
    aLink.href = `/${defaultSettings.apiName}/statement/getPayExcel.sp?${paramsStr.substr(1)}`;
    aLink.setAttribute('download', '支付记录');
    aLink.click();
    document.body.removeChild(aLink);
    hide();
    return true;
  } catch (error) {
    hide();
    message.error('导出Excel失败请重试！');
    return false;
  }
};

const TableList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<PAYS.PayListItem>();
  const { fetchDicList, dicList } = useModel('dictionary');
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
    fetchDicList('sysType');
  }, []);

  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }

  const columns: ProColumns<PAYS.PayListItem>[] = [
    {
      title: '支付时间',
      dataIndex: 'payTime',
      valueType: 'dateTimeRange',
      width: 100,
      key: 'payTime',
      hideInTable: true,
      search: {
        transform: (value) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (saID, record) => {
        return record.hospitalName
      },
      width: 120,
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        return (
          <Select {...rest} placeholder="请选择">
            {hosList &&
              hosList.map((c: ListType) => {
                return (
                  <Option value={c.value} key={`hos${c.value}`}>
                    {c.label}
                  </Option>
                );
            })}
          </Select>
        );
      },
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      width: 100,
      key: 'patientName'
    },
    {
      title: '终端编号',
      dataIndex: 'deviceCode',
      width: 80,
      key: 'deviceCode'
    },
    {
      title: "系统类型",
      dataIndex: 'sysType',
      key: 'sysType',
      renderText: (saID, record) => {
        return record.sysTypeName
      },
      width: 120,
      renderFormItem: (item, { defaultRender, ...rest }) => {
        console.log(dicList)
        return (
          <Select {...rest} placeholder="请选择">
            {
              dicList && dicList.map((c) => {
                return (<Option value={c.value} key={`sysType${c.value}`}>{c.label}</Option>)
              })
            }
          </Select>
        )
      },
    },
    {
      title: '介质类型',
      dataIndex: 'cardType',
      width: 100,
      key: 'cardType',
      hideInTable: true,
      valueEnum: typeObjectFormat('CARD_TYPE')
    },
    {
      title: '支付类型',
      dataIndex: 'payBusinessType',
      width: 100,
      key: 'payBusinessType',
      valueEnum: typeObjectFormat('PAY_BUSINESS_TYPE')
    },
    {
      title: '商户订单号',
      dataIndex: 'outTradeNo',
      width: 100,
      key: 'outTradeNo',
      hideInForm: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: '第三方流水号',
      dataIndex: 'productID',
      width: 100,
      key: 'productID',
    },
    {
      title: '支付金额',
      dataIndex: 'totalAmount',
      hideInSearch: true,
      hideInForm: true,
      width: 60,
      key: 'totalAmount',
    },
    {
      title: '支付方式',
      dataIndex: 'payType',
      width: 80,
      key: 'payType',
      valueEnum: typeObjectFormat('PAY_TYPE'),
    },
    {
      title: '支付状态',
      dataIndex: 'state',
      width: 100,
      key: 'state',
      valueEnum: typeObjectFormat('PAY_STATE'),
    },
    {
      title: 'HIS结算状态',
      dataIndex: 'hisState',
      width: 100,
      key: 'hisState',
      valueEnum: {
        1: {
          text: '成功',
          status: 'Success'
        },
        2: {
          text: '失败',
          status: 'Error'
        }
      },
      hideInTable: true,
    },
    {
      title: '异常状态',
      dataIndex: 'unusualState',
      width: 100,
      key: 'unusualState',
      valueEnum: {
        1: {
          text: '正常',
          status: 'Success'
        },
        2: {
          text: '用户短款异常',
          status: 'Error'
        },
        3: {
          text: '医院短款异常',
          status: 'Error'
        },
      },
      hideInTable: true,
    },
    {
      title: '支付创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      width: 100,
      key: 'createTime',
      hideInSearch: true,
      hideInForm: true,
      hideInTable: true,
    },

    {
      title: '支付时间',
      dataIndex: 'payTime',
      width: 100,
      key: 'payTimeStr',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      hideInForm: true,
      hideInSearch: true,
      width: 100,
      render: (_, record) => (
        <>
          <a onClick={()=> {
            setCurrentRow(record);
            setShowDetail(true)
          }}>
            详情<BarsOutlined />
          </a>
          <Divider type="vertical"/>
          <a
            onClick={() => {
              const success = handleQueryPay(record)
              if (success && actionRef.current) {
                actionRef.current.reload();
              }
            }}
          >刷新
          </a>
        </>
      )
    }
  ];
  return (
    <PageContainer>
      <ProTable<PAYS.PayListItem, PAYS.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
          optionRender: (searchConfig, formProps, dom) => [
            ...dom.reverse(),
            <Button key="out" onClick={() => {
              const values = searchConfig?.form?.getFieldsValue();
              handleExportToExcel(values);
            }}>
              导出
            </Button>,
          ],
        }}
        toolBarRender={() => []}
        request={queryPayList}
        columns={columns}
      />
      { showDetail && <OrderDetail visible={showDetail} data={currentRow} onCancel={() => { setShowDetail(false); setCurrentRow({})}} />}
    </PageContainer>
    
  );
};
export default TableList;
