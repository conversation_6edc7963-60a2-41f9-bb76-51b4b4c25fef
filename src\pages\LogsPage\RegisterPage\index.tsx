import { queryRegisterList } from '@/services/api/logs';
import { PlusOutlined, SyncOutlined, PhoneOutlined} from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, Input, message } from 'antd';
import React, { useRef, useState, useEffect, createRef} from 'react';
import { useModel } from '@umijs/max';
import RomateVideo from '@/components/RomateVideo/index_srs';
import RomateVideoCalled from '@/components/RomateVideo/index_srs_called';
import { useRequest } from 'ahooks';
import { createRoom, editRoom, getRoomStatusByRoomId} from '@/services/api/audio';
import { encrypt } from '@/utils'
import './index.less'

export type tokenType = {
    userID?: string;
    channelID?: string;
    token?: string
    userName?: string;
    status?: number;
    state?: number;
    roomId?: string;
};

/**
 * @zh-CN 请求频道状态
 * @param fields
 */
const handleGetChannelStatusByChannel = async (fields: tokenType) => {
  try {
    const response = await getRoomStatusByRoomId({ ...fields });
    if (response.code === '0' &&  response.data && response.data?.state === 1) {
      return response.data;
    }
    return false;
  } catch (error) {
    message.error('网络异常');
    return false;
  }
};

const TableList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { fetchDicList, dicList } = useModel('dictionary');
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<LOGS.LogListItem>();
  const [selectedRowsState, setSelectedRows] = useState<LOGS.LogListItem[]>([]);
  /* 视频通话逻辑参数 平台主动呼叫 */ 
  const [isLogin, setIsLogin] = useState<boolean>(false)
  const [deviceCode, setDeviceCode] = useState('')
  
  const romateVideoRef = createRef<any>()
  const romateVideoCalledRef = createRef<any>()
  const [isOpenRecycle, setIsOpenRecycle] = useState<boolean>(false);
  const [isCalled, setIsCalled] = useState<boolean>(false);
  

  /* 视频通话逻辑参数 平台被呼叫 */ 
  useEffect(() => {
    fetchDicList('operatingType');
  }, []);

  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const callAudio = async (value) => {
    if(!value) return message.error('该记录未绑定设备号，无法呼叫')
    if(isLogin) return message.error('正在通话其他设备中')
    setIsLogin(true)
    setDeviceCode(value)
    // romateVideoRef.current?.call({
    //   // userID: '1234',
    //   channelID: '**********',
    //   userName: initialState?.currentUser.realName,
    //   // userName: 'admin'
    // })
  }

  const { data,  run , cancel } = useRequest(() => handleGetChannelStatusByChannel({
    roomId: encrypt(initialState?.currentUser.realName)
  }), {
    refreshDeps: [],
    pollingInterval: 3000,
    pollingWhenHidden: true,
    manual: true
  });

  useEffect(() => {
    if (romateVideoRef.current?.call && deviceCode) {
      romateVideoRef.current?.call({
        // userID: '1234',
        channelID: deviceCode,
        userName: initialState?.currentUser.realName,
        // userName: 'admin'
      })
    }
  }, [romateVideoRef.current?.call, deviceCode])

  useEffect(() => {
    if(data && data.state === 1){
      // 有视频通话呼入
      console.log('呼叫呼入', data);
      setIsCalled(true);  // 设置呼叫状态
      setDeviceCode(data.userName);
      cancel();  // 取消轮询
    }
  }, [data])

  useEffect(() => {
    if (romateVideoCalledRef.current?.apply && deviceCode) {
      romateVideoCalledRef.current?.apply({
        channelID: initialState?.currentUser.realName,
        userName: deviceCode,
      })
    }
  }, [romateVideoCalledRef.current?.apply, deviceCode])

  const handleRecycleRomate = (isOpen: boolean) => {
    // 开启接听
    setIsOpenRecycle(isOpen)
    if(isOpen){
      run();
    }else{
      cancel()
    }
  }

  const handleCalledStatus = () => {
    setIsCalled(false)
    run();
  }

  const columns: ProColumns<LOGS.LogListItem>[] = [
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      width: 100,
      key:'patientName'
    },
    {
      title: '科室名称',
      dataIndex: 'deptName',
      width: 100,
      key:'deptName',
      hideInSearch: true,
    },
    {
      title: '患者身份证号',
      dataIndex: 'idCard',
      width: 100,
      key:'idCard',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '患者ID',
      dataIndex: 'patientID',
      width: 100,
      key:'patientID',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '挂号费',
      dataIndex: 'regFee',
      hideInSearch: true,
      hideInForm: true,
      width: 100,
      key:'regFee'
    },
    {
      title: '挂号状态',
      dataIndex: 'state',
      width: 100,
      key:'state',
      valueEnum: {
        0: {
          text: '预约',
        },
        1: {
          text: '挂号',
        },
        2: {
          text: '取消预约',
        },
        3: {
          text: '退号',
        },
      },
    },
    {
      title: '挂号时间',
      dataIndex: 'createTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      key:'createTime'
    },
    {
      title: '挂号时间',
      dataIndex: 'createTime',
      width: 150,
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '就诊时间',
      dataIndex: 'registrationTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      key:'registrationTime'
    },
    {
      title: '就诊时间',
      dataIndex: 'registrationTime',
      width: 150,
      key: 'registrationTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record) => [
        <a 
          key="media"
          onClick={async () => {
            await callAudio(record.deviceCode)
            //  await callAudio('PH1423BT90010')
          }}
        >
          视频通话
        </a>
    ]}
  ];
  return (
    <>
    <PageContainer>
      <ProTable<LOGS.LogListItem, LOGS.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            size="large"
            onClick={() => {
              if(isOpenRecycle){
                return handleRecycleRomate(false)
              } else {
                return handleRecycleRomate(true)
              }
            }}
          >
            { isOpenRecycle ? <> <SyncOutlined spin/>&nbsp;&nbsp;关闭接听</> : <> <PhoneOutlined  />&nbsp;&nbsp;开启接听</> }
          </Button>,
        ]}
        request={queryRegisterList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.requestAction && (
          <ProDescriptions
            column={1}
            title={currentRow?.requestAction}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<LOGS.LogListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
    { isLogin && <RomateVideo ref={romateVideoRef} setIsLogin={setIsLogin} setDeviceCode={setDeviceCode} /> }
    { isCalled && <RomateVideoCalled ref={romateVideoCalledRef} setIsCalled={handleCalledStatus} setDeviceCode={setDeviceCode} />} 
    </>
  );
};
export default TableList;
