import { queryRegisterList } from '@/services/api/logs';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, Input, message } from 'antd';
import React, { useRef, useState, useEffect } from 'react';
import { useModel } from '@umijs/max';

const TableList: React.FC = () => {
  const { fetchDicList, dicList } = useModel('dictionary');
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<LOGS.LogListItem>();
  const [selectedRowsState, setSelectedRows] = useState<LOGS.LogListItem[]>([]);

  useEffect(() => {
    fetchDicList('operatingType');
  }, []);

  const typeObject = {};
  for (let i = 0; i < dicList.length; i++) {
    typeObject[dicList[i].value] = { text: dicList[i].label };
  }
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */
  const columns: ProColumns<LOGS.LogListItem>[] = [
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      width: 100,
      key:'patientName'
    },
    {
      title: '科室名称',
      dataIndex: 'deptName',
      width: 100,
      key:'deptName',
      hideInSearch: true,
    },
    {
      title: '患者身份证号',
      dataIndex: 'idCard',
      width: 100,
      key:'idCard',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '患者ID',
      dataIndex: 'patientID',
      width: 100,
      key:'patientID',
      hideInSearch: true,
      hideInForm: true,
    },
    {
      title: '挂号费',
      dataIndex: 'regFee',
      hideInSearch: true,
      hideInForm: true,
      width: 100,
      key:'regFee'
    },
    {
      title: '挂号状态',
      dataIndex: 'state',
      width: 100,
      key:'state',
      valueEnum: {
        0: {
          text: '预约',
        },
        1: {
          text: '挂号',
        },
        2: {
          text: '取消预约',
        },
        3: {
          text: '退号',
        },
      },
    },
    {
      title: '挂号时间',
      dataIndex: 'createTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      key:'createTime'
    },
    {
      title: '挂号时间',
      dataIndex: 'createTime',
      width: 150,
      key: 'createTime',
      hideInSearch: true,
    },
    {
      title: '就诊时间',
      dataIndex: 'registrationTime',
      valueType: 'dateTimeRange',
      hideInTable: true,
      key:'registrationTime'
    },
    {
      title: '就诊时间',
      dataIndex: 'registrationTime',
      width: 150,
      key: 'registrationTime',
      hideInSearch: true,
    },
  ];
  return (
    <PageContainer>
      <ProTable<LOGS.LogListItem, LOGS.PageParams>
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => []}
        request={queryRegisterList}
        columns={columns}
      />
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.requestAction && (
          <ProDescriptions
            column={1}
            title={currentRow?.requestAction}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<LOGS.LogListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};
export default TableList;
