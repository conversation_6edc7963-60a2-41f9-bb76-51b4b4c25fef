import { ModalForm, ProFormSelect, ProFormText} from '@ant-design/pro-form';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<DEPT.DeptListItem>;

type UpdateFormProps = {
	onCancel: (flag?: boolean, formVals?: FormValueType) => void;
	onSubmit: (values: FormValueType) => Promise<void>;
	modalVisible: boolean;
  values: FormValueType;
};

const DiagSettingsForm: React.FC<UpdateFormProps> = ({ modalVisible, onCancel, onSubmit , values }) => {
  return (
    <ModalForm
      title="修改诊区叫号规则"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <ProFormText name="digID" hidden/>
      <ProFormSelect 
        name="doctorNum"
        label="诊区医生候诊阈值"
        request={async () => [
          { label: '无候诊阈值', value: 0 },
          { label: '一位患者', value: 1 },
          { label: '两位患者', value: 2 },
          { label: '三位患者', value: 3 },
          { label: '四位患者', value: 4 },
          { label: '五位患者', value: 5 },
          { label: '六位患者', value: 6 },
          { label: '七位患者', value: 7 },
          { label: '八位患者', value: 8 },
          { label: '九位患者', value: 9 },
          { label: '十位患者', value: 10 },
          { label: '十五位患者', value: 15 },
          { label: '二十位患者', value: 20 },
        ]}
        colProps={{ span: 12 }}
      />
      <ProFormSelect
        name="backNum"
        label="回诊/过号患者规则"
        request={async () => [
          { label: '隔一插一', value: 1 },
          { label: '隔二插一', value: 2 },
          { label: '隔三插一', value: 3 },
          { label: '隔四插一', value: 4 },
          { label: '隔五插一', value: 5 },
        ]}
        colProps={{ span: 12 }}
      />
      <ProFormSelect
        name="priorityNum"
        label="优先患者规则"
        request={async () => [
          { label: '隔一插一', value: 1 },
          { label: '隔二插一', value: 2 },
          { label: '隔三插一', value: 3 },
          { label: '隔四插一', value: 4 },
          { label: '隔五插一', value: 5 },
        ]}
        colProps={{ span: 12 }}
      />
    </ModalForm>
  );
};

export default DiagSettingsForm;