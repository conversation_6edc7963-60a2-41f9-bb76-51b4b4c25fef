import {
  PlusOutlined,
  InfoCircleOutlined,
  StopOutlined,
  TeamOutlined,
  SettingOutlined,
  DesktopOutlined,
  CheckOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { Button, Modal, Drawer, Select, Menu, Empty, Tag, Progress, Row, Col, Space, message, Segmented} from 'antd';
import { useState, useRef, useEffect, useCallback} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ProCard, ProList } from '@ant-design/pro-components';
import { queryDocList, updateDoc, addDoc, getDeptInfo, updateDeptInfo, updateDiagArea} from '@/services/api/hospital';
import { getDigDeptDoctorList,
  getDigAreaDivisionalInfo,
  getDeptDivisionalInfo,
  divisionalCountByDoctor,
  divisionalCountByHour,
  doctorCheckOut
} from '@/services/api/queue';
import { useModel } from '@umijs/max';
import { useFullscreen } from 'ahooks';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import { handleOperateMethod } from '@/utils/index';
import CompoundedSpace from 'antd/lib/space';
import moment from 'moment';
import styles from './index.less';
import SettingsForm from './components/SettingsForm';
import DiagSettingsForm from './components/DiagSettingsForm'
import DigSelectMenu from './views/DigSelectMenu';
import DigSelectDocMenu from './views/DigSelectDocMenu';
import DigAreaList from './views/DigAreaList';
import DigCountByHour from './views/DigCountByHour';
import DigCountByDept from './views/DigCountByDept';
import DeptStateList from './views/DeptStateList';
import DeptDeviceList from './views/DeptDeviceList';
import DeptTable from './tables/DeptTable';
import DocList from './components/DocList';
const { Option } = Select

/**
 * @zh-CN 修改科室
 * @param fields
 */
const handleDeptUpdate = (fields) => handleOperateMethod(updateDeptInfo, fields, 'update');

/**
 * @zh-CN 修改医生
 * @param fields
 */
const handleDocUpdate = (fields) => handleOperateMethod(updateDoc, fields, 'update');

/**
 * @zh-CN 修改修改诊区
 * @param fields
 */
const handleDiagUpdate = (fields) => handleOperateMethod(updateDiagArea, fields, 'update');

/**
 * 医生签退
 * @param fields
 */
const handleUpdateDoctor = async (fields) => {
  const hide = message.loading('正在签退医生');
  try {
    const response = await doctorCheckOut({
      ...fields
    });
    if(response.code === '0') {
      hide();
      message.success('医生签退成功，稍后列表将刷新！');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('医生签退失败请重试！');
    return false;
  }
};

const Queue = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [ digList, setDigList ] = useState([])
  /** 诊区设置的弹窗 */
  const [digSettingsModalVisible, handleDigSettingsModalVisible] = useState(false);
  /** 诊区广播的弹窗 */
  const [digDeviceModalVisible, handleDigDeviceModalVisible] = useState(false);
  /** 科室设置的弹窗 */
  const [settingsModalVisible, handleSettingsModalVisible] = useState(false);
  /** 医生列表的弹窗 */
  const [doctorListVisible, handleDoctorListVisible] = useState(false);
  /** 医生列表的弹窗 */
  const [docSettingsModalVisible, handleDocSettingsModalVisible] = useState(false);
  const actionRef = useRef();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState();
  const { hosList, 
    fetchHosList, 
    deptList, 
    fetchDeptList, 
    fetchDeptStatus, 
    deptStatus, 
    fetchDiagStatus, 
    diagStatus,
    fetchDoctorStatus,
    doctorStatus
  } = useModel('hospital');
  
  const [digDeptDoctorList, setDigDepDoctorList] = useState([]);
  const [selectDig, setSelectDig] = useState() // 选择的诊区
  const [menuSelectType, setMenuSelectType] = useState("1") // 选择的菜单类型 1按科室 2按医生  默认是1
  const [menuListSelect, setMenuListSelect] = useState()  // 菜单选中的key
  const [openKeys, setOpenKeys]= useState([]) // 菜单选中的打开
  const [selectDept, setSelectDept] = useState();   // 选择的科室
  const [selectDoc, setSelectDoc] = useState();     // 选择的医生
  const [menuType, setMenuType] = useState();       // 选择的菜单类型
  const [digAreaData, setDigAreaData] = useState([]);  // 诊区总览数据区块可视化
  const [digCountByHourData, setDigCountByHourData] = useState([]) // 诊区运营就诊人次趋势图
  const [digCountByDeptData, setDigCountByDeptData] = useState([]) // 诊区运营科室排行榜图
  const ref = useRef(null);

  const [isFullscreen, { enterFullscreen, exitFullscreen, toggleFullscreen }] = useFullscreen(ref);

  useEffect(() => {
    fetchHosList();
    fetchDeptList();
    // 请求账号诊区权限并设置
    const digRes = currentUser?.digAreaList?.map(item => {
      return {
        label: item.digAreaName,
        value: item.digAreaCode
      }
    })
    if(!digRes.length > 0){
      message.error('该诊区权限！')
    }
    setDigList(digRes)
  }, []);

  useEffect(() => {
    if(digList.length > 0){ 
      handleDigSelect(digList[0].value)
    }
  }, [digList]);

  // 切换菜单类型
  const handleMenuSelectType = (key) => {
    setMenuSelectType(key);
  }

  // 处理诊区下拉选择后的切换
  const handleDigSelect = (key) => {
    setSelectDig(digList.find(item => key === item.value))
  }

  // 处理选中科室医生菜单后的切换
  const handleMenuSelect = ({item, key, keyPath}) => {
    setMenuListSelect(key)
    const splitArr = key.split('|');
    const code = splitArr[0]
    const type = splitArr[1]
    const label = splitArr[2]
    let deptID, roomName;
    if(splitArr.length > 3){
      deptID =  key.split('|')[3]
    }
    if(splitArr.length > 4){
      roomName = key.split('|')[4]
    }
    setMenuType(type)
    if(type === 'dig'){
      fetchDiagStatus(code)
    } else if(type === 'dept'){
      setSelectDept({key, label, code, deptCode: code, deptID})
      fetchDeptStatus(deptID)
      if (openKeys.indexOf(code) === -1 && roomName ==='HASDOC') {
        openKeys.push(code)
        setOpenKeys(openKeys)
      } 
    }else if(type === 'doc'){
      setSelectDoc({key, label, code, doctorCode: code, deptCode: deptID, roomName: roomName})
      fetchDoctorStatus({
        doctorCode: code,
        deptCode: deptID
      })
    }
  }

  const handleSignInOpenOrClose = async (signStatus) => {
    Modal.confirm({
      title: '确认',
      icon: <InfoCircleOutlined />,
      content:  `是否设置“${selectDept.label}”${signStatus === 1 ?'可以签到':'不可签到'}?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const success = await handleDeptUpdate({
          deptID: parseInt(selectDept.deptID),
          signStatus // 1 可签 2 不可签
        })
        if(success){
          fetchDeptStatus(selectDept.deptID)
        }
      }
    });
  }

  const handleDocCheckOut = async () => {
    Modal.confirm({
      title: '确认',
      icon: <InfoCircleOutlined />,
      content:  `是否为${selectDoc.label}医生签退?`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const success = await handleUpdateDoctor({
          doctorCode: selectDoc.doctorCode,
          deptCode:  selectDoc.deptCode
        })
        if(success){
          handleMenuSelect({key: `${selectDig.value}|dig|${selectDig.label}`})
          // fetchDeptStatus(selectDept.deptID)
        }
      }
    });
  }

  return (
    <div ref={ref} className={isFullscreen ? styles.wrapperDiv : ''}>
    <PageContainer header={{ title: <>
        <span style={{marginRight: 20}}>排队管理</span>
        { digList.length > 0 && selectDig ? 
          <Select
            defaultValue={selectDig.value}
            style={{ width: 150 }}
            bordered={false}
            options={digList}
            onSelect={handleDigSelect}
          />: null
        }
    </>,
      breadcrumb:{}} 
    } extra={isFullscreen ? <Button icon={<FullscreenExitOutlined />} onClick={exitFullscreen}>退出全屏</Button> : <Button icon={<FullscreenOutlined />} onClick={enterFullscreen}>进入全屏</Button>} 
    >
      <ProCard split="vertical">
        <ProCard title="诊区菜单" colSpan="15%" bodyStyle={{padding: '20px 0'}} extra={<Segmented size="small" options={[{
            label: '科室',
            value: '1',
          },{
            label: '医生',
            value: '2',
          }]} value={menuSelectType} onChange={handleMenuSelectType} />}>
          { selectDig && menuSelectType === "1" && 
            <DigSelectMenu 
              digData={selectDig}
              code={selectDig.value}
              type={menuSelectType}
              handleMenuSelect={handleMenuSelect}
              menuListSelect={menuListSelect}
              setMenuListSelect={setMenuListSelect}
              openKeys={openKeys}
              setOpenKeys={setOpenKeys}
            />
          }
          { selectDig && menuSelectType === "2" && 
            <DigSelectDocMenu 
              digData={selectDig}
              code={selectDig.value}
              type={menuSelectType}
              handleMenuSelect={handleMenuSelect}
              menuListSelect={menuListSelect}
              setMenuListSelect={setMenuListSelect}
              openKeys={openKeys}
              setOpenKeys={setOpenKeys}
            />
          }
        </ProCard>
        <ProCard title="" headerBordered>
          <div style={{minHeight: "100vh"}}>
            {
              menuType === 'dig' && <>
              <div className={styles.header}>
                <span className={styles.digName}>{selectDig.label}</span>
                <span className={styles.title}>诊区运营总览</span>
                {/* <span className={styles.time}>数据更新时间：{moment().format('YYYY-MM-DD HH:mm:ss')}</span> */}
                <Space>
                  <Button icon={<SettingOutlined/>} onClick={()=>{ handleDigSettingsModalVisible(true)}}>诊区设置</Button>
                  <Button icon={<DesktopOutlined/>} onClick={()=>{ handleDigDeviceModalVisible(true)}}>设备管理</Button>
                </Space>
              </div>
              { selectDig && <DigAreaList code={selectDig.value} handleMenuSelect={handleMenuSelect} setMenuListSelect={setMenuListSelect} /> }
                <Row gutter={16}>
                  <Col span={16}>
                    { selectDig && <DigCountByHour code={selectDig.value}/> }
                  </Col>
                  <Col span={8}>
                    { selectDig && <DigCountByDept code={selectDig.value}/> }
                  </Col>
                </Row>
              </>
            }
            {
              menuType === 'dept' && <>
                <div className={styles.header}>
                  <span className={styles.digName}>{selectDept.label}</span>
                  <span className={styles.title}>科室运行总览</span>
                  <span className={styles.time}>
                    <Space>
                      {deptStatus?.signStatus === 2 ? 
                      <Button icon={<CheckOutlined />} onClick={() => handleSignInOpenOrClose(1)}>开放签到</Button>:
                      <Button icon={<StopOutlined/>} onClick={() => handleSignInOpenOrClose(2)}>限制签到</Button> }
                      {/* <Button icon={<TeamOutlined/>} onClick={() => handleDoctorListVisible(true)}>科室医生</Button> */}
                      <Button icon={<SettingOutlined/>} onClick={()=> handleSettingsModalVisible(true)}>科室设置</Button>
                    </Space>
                  </span>
                </div>
                { selectDept && <DeptStateList code={selectDept.code}/> }
                <div className={styles.table}>
                  { selectDept && <DeptTable code={selectDept.code} customParams={{...selectDept, digCode: selectDig.value}}/>}
                </div>
              </>
            }
            {
              menuType === 'doc' && 
              <>
                <div className={styles.header}>
                  <span className={styles.digName}>{selectDoc.label}</span>
                  <span className={styles.title}>医生看诊情况</span>
                  <div className={styles.right}>
                    <span className={styles.roomName}>{selectDoc.roomName}</span>
                    <Space>
                      <Button icon={<SettingOutlined/>} onClick={()=> handleDocSettingsModalVisible(true)}>医生设置</Button>
                      <Button icon={<LogoutOutlined />} onClick={()=> handleDocCheckOut()}>医生签退</Button>
                    </Space>
                  </div>
                </div>
                <div >
                  { selectDoc && <DeptTable code={selectDoc.code} customParams={{...selectDoc, digCode: selectDig.value}}/>}
                </div>
              </>
            }
          </div>
        </ProCard>
      </ProCard>
      {settingsModalVisible && (
        <SettingsForm
          title="修改科室叫号规则"
          values={deptStatus || {}}
          modalVisible={settingsModalVisible}
          onCancel={() => handleSettingsModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleDeptUpdate({...value, deptID: parseInt(selectDept.deptID)});
            if (success) {
              fetchDeptStatus(selectDept.deptID)
              handleSettingsModalVisible(false);
            }
          }}
        />
      )}
      {docSettingsModalVisible && (
        <SettingsForm
          title="修改医生叫号规则"
          values={doctorStatus || {}}
          modalVisible={docSettingsModalVisible}
          onCancel={() => handleDocSettingsModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleDocUpdate({...value, docID: doctorStatus?.docID});
            if (success) {
              fetchDoctorStatus({
                doctorCode: doctorStatus?.doctorCode,
                deptCode: doctorStatus?.deptCode
              })
              handleDocSettingsModalVisible(false);
            }
          }}
        />
      )}
      {
        digSettingsModalVisible && <DiagSettingsForm
          values={diagStatus || {}}
          modalVisible={digSettingsModalVisible}
          onCancel={() => handleDigSettingsModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleDiagUpdate({...value });
            if (success) {
              fetchDiagStatus(selectDig.value)
              handleDigSettingsModalVisible(false);
            }
          }}
        />
      }
      {
        digDeviceModalVisible && <DeptDeviceList
          values={diagStatus || {}}
          modalVisible={digDeviceModalVisible}
          onCancel={() => handleDigDeviceModalVisible(false)}
          onSubmit={async (value) => {
            const success = await handleDiagUpdate({...value });
            if (success) {
              fetchDiagStatus(selectDig.value)
              handleDigDeviceModalVisible(false);
            }
          }}
        />
      }
      {
        doctorListVisible && (
          <DocList
            values={deptStatus || {}}
            modalVisible={doctorListVisible}
            onCancel={() => handleDoctorListVisible(false)}
          />
        )
      }
    </PageContainer>
    </div>
  );
};

export default Queue;
