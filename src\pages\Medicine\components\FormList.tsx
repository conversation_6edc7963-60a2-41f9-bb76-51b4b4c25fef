import { ProFormText, ProFormSelect, ProFormDigit} from '@ant-design/pro-form';
import { Row, Col } from 'antd';
import { useModel } from '@umijs/max';

const FormList = () => {
  const { fetchDicList3 } = useModel('dictionary');
  const { fetchHosList, fetchFrequencyList, fetchUsageList} = useModel('hospital');
  const { initialState } = useModel('@@initialState');
  return (
    <>
      <Row gutter={24} >
        <Col span={12}>
        { initialState.currentUser.saID ? <ProFormText
            initialValue={initialState.currentUser.saID}
            name="saID"
            label="机构名称"
            hidden
          /> : <ProFormSelect
            name="saID"
            label="机构名称"
            rules={[
              {
                required: true,
                message: '机构名称为必填项',
              },
            ]}
            colProps={{ span: 12 }}
            request={() => fetchHosList()}
            showSearch
          />
        }
        </Col>
        <Col span={12}>
        <ProFormText
          rules={[
            {
              required: true,
              message: '药品名称为必填项',
            },
          ]}
          name="ypmc"
          label="药品名称"
          placeholder="请输入药品名称"
        />
        </Col>
        <Col span={12}>
        <ProFormText 
          rules={[
            {
              required: true,
              message: '医保代码为必填项',
            },
            {
              pattern: /^.{20}$|^.{23}$/,
              message: '医保代码长度为20位或23位'
            }
          ]} 
          name="ybdm" 
          label="医保代码"
          placeholder="请输入医保代码"
        />
        </Col>
        <Col span={12}>
        <ProFormText
          name="ypxh"
          label="条形码"
          placeholder="请输入药品条形码"
        />
        </Col>
        <Col span={12}>
          <ProFormText
            name="sccj"
            label="生产厂家"
            rules={[
              {
                required: true,
                message: '生产厂家为必填项',
              },
            ]}
            placeholder="请输入药品生产厂家"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="jx"
            label="剂型"
            rules={[
              {
                required: true,
                message: '剂型为必填项',
              }
            ]} 
            placeholder="请选择药品剂型"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="gg"
            label="药品规格"
            rules={[
              {
                required: true,
                message: '药品规格为必填项',
              },
            ]}
            placeholder="请输入药品总规格"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="ypgg"
            label="小计量规格"
            placeholder="请输入药品小计量规格"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="dw"
            label="单位"
            rules={[
              {
                required: true,
                message: '单位为必填项',
              }
            ]} 
            // request={() => fetchDicList3('dwType')}
            placeholder="请输入药品包装单位"
            // showSearch
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="jldw"
            label="小计量单位"
            // request={() => fetchDicList3('jldwType')}
            rules={[
              {
                required: true,
                message: '小计量单位为必填项',
              }
            ]}
            placeholder="请输入药品小计量单位"
            // showSearch
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="gytj"
            label="用法"
            request={() => fetchUsageList()}
            placeholder="请输入用法"
            showSearch
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="pc"
            label="频次"
            placeholder="请输入频次"
            request={() => fetchFrequencyList()}
            showSearch
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="dcyl"
            label="单次用量"
            placeholder="请输入单次用量（数字）"
            rules={[
              { validator: (_, value, callback) => {
                if (value && !value.match(/^[0-9]+$/)) {
                    callback('请输入数字');
                  }else{
                    callback()
                  }
                }
              }]
            }
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="yyts"
            label="用药天数"
            placeholder="请输入用药天数"
            rules={[
              { validator: (_, value, callback) => {
                  if (value && !value.match(/^[0-9]+$/)) {
                    callback('请输入数字');
                  }else{
                    callback()
                  }
                }
              }]
            }
          />
        </Col>
        <ProFormText
          name="state"
          label="状态"
          hidden
        />
       </Row>
    </>
  );
};

export default FormList;
