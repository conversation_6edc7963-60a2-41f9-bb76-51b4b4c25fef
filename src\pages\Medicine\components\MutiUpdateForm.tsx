import { ModalForm} from '@ant-design/pro-form';
import { Alert } from 'antd';
import FormList from './FormList';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
};

type FormValueType = Partial<MEDICINE.MedicineListItem[]>;

export type MutiUpdateFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  updateModalVisible: boolean;
  values: FormValueType;
};

const MutiUpdateForm: React.FC<MutiUpdateFormProps> = ({ updateModalVisible, values, onCancel, onSubmit }) => {
  return (
    <ModalForm
      title="批量修改药品"
      layout="horizontal"
      width={640}
      open={updateModalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
      }}
      onFinish={onSubmit}
      {...formItemLayout}
      initialValues={{ ...values }}
      className="_modal-wrapper"
    >
      <Alert message="Success Text" type="success" />
      <FormList />
    </ModalForm>
  );
};

export default MutiUpdateForm;
