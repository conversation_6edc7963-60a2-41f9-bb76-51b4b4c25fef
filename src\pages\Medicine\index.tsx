import {
  PlusOutlined,
  InfoCircleOutlined,
  UploadOutlined,
  EditOutlined
} from '@ant-design/icons';
import { But<PERSON>, Modal, Drawer, Select, Upload, message} from 'antd';
import type {UploadProps} from 'antd';
import { useState, useRef, useEffect} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryMedicineList, updateMedicine, addMedicine } from '@/services/api/hospital';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import CreateForm from './components/CreateForm';
import UpdateForm from './components/UpdateForm';
import defaultSettings from '../../../config/defaultSettings';
import { handleOperateMethod } from '@/utils/index';
const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 添加药品
 * @param fields
 */
const handleAdd = (fields: MEDICINE.MedicineListItem) => handleOperateMethod(addMedicine, fields, 'add');
/**
 * @zh-CN 修改药品
 * @param fields
 */
const handleUpdate = (fields: MEDICINE.MedicineListItem) => handleOperateMethod(updateMedicine, fields, 'update');
/**
 * @zh-CN 删除药品
 * @param deviceID
 */
const handleRemove = (id?: string) =>
  handleOperateMethod(updateMedicine, { id, state: 9 }, 'delete');

const TableList = () => {
  const { initialState } = useModel('@@initialState');
  /** 新增药品的弹窗 */
  const [createModalVisible, handleModalVisible] = useState(false);
  /** 修改药品的弹窗 */
  const [updateModalVisible, handleUpdateModalVisible] = useState(false);
  /** 展示药品详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  /** 多选 */
  const [selectedRowsState, setSelectedRows] = useState<MEDICINE.MedicineListItem[]>([]);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */ 
  const [currentRow, setCurrentRow] = useState<MEDICINE.MedicineListItem>();
  const { hosList, fetchHosList, usagesList ,fetchUsageList, frequencyList, fetchFrequencyList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
    fetchUsageList();
    fetchFrequencyList();
  }, []);

  const action = `/${defaultSettings.apiName}/medicine/saveMedicineExcel.sp`;
  const uProps: UploadProps = {
		name: 'file',
    method: 'POST',
		action,
		showUploadList: false,
		accept: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",
		multiple: false,
		onChange(info) {
			if (info.file.status == 'uploading') {
        // message.loading({content: '文件正在上传中，请稍侯...'})
				// console.log(info.file, info.fileList);
			}
			if (info.file.status === 'done') {
				if (info.file.response.code === "0") {
					message.success({ content: info.file.response.msg, duration: 3 });
					actionRef.current?.reload?.();
				}
				if (info.file.response.code === "2") {
					message.success({ content: info.file.response.msg, duration: 5 });
				}
			} else if (info.file.status === 'error') {
				message.error(`${info.file.name} file upload failed.`);
			}
		},
	};

  const columns: ProColumns<MEDICINE.MedicineListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      key: 'saID',
      hideInSearch: !initialState.currentUser.saID ? false : true,
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select {...rest} placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear/>
        );
      },
      colSize: 2,
    },
    {
      title: '机构代码',
      dataIndex: 'saID',
      key: 'saID',
      hideInTable: true,
      hideInSearch: !initialState.currentUser.saID ? false: true,
    },
    {
      title: '药品名称',
      dataIndex: 'ypmc',
      key: 'ypmc',
    },
    {
      title: '医保代码',
      dataIndex: 'ybdm',
      key: 'ybdm',
    },
    {
      title: '条形码',
      dataIndex: 'ypxh',
      key: 'ypxh',
    },
    {
      title: '剂型',
      dataIndex: 'jx',
      key: 'jx',
      hideInSearch: true,
    },
    {
      title: '规格',
      dataIndex: 'gg',
      key: 'gg',
      hideInSearch: true,
    },
    // {
    //   title: '小计量规格',
    //   dataIndex: 'ypgg',
    //   key: 'ypgg',
    //   hideInSearch: true,
    // },
    {
      title: '单位',
      dataIndex: 'dw',
      key: 'dw',
      hideInSearch: true,
    },
    {
      title: '小计量单位',
      dataIndex: 'jldw',
      key: 'jldw',
      hideInSearch: true,
    },
    {
      title: '用法用量',
      dataIndex: 'yfylfot',
      key: 'yfylfot',
      renderText: (_, record) => {
        const yf = record.gytj && usagesList && usagesList.find(item => item.value === record.gytj)?.label || ""
        const pc = record.pc && frequencyList && frequencyList.find(item => item.value === record.pc)?.label || ""
        const dc = record.dcyl && record.jldw && `一次${record.dcyl}${record.jldw}` || ""
        return `${yf? `${yf}，`: ""}${pc? `${pc}，`: ""}${dc ? dc: ""}`
      },
    },
    // {
    //   title: '用法',
    //   dataIndex: 'gytj',
    //   key: 'gytj',
    //   hideInSearch: true,
    // },
    // {
    //   title: '频次',
    //   dataIndex: 'pc',
    //   key: 'pc',
    //   hideInSearch: true,
    // },
    // {
    //   title: '单次用量',
    //   dataIndex: 'dcyl',
    //   key: 'dcyl',
    //   hideInSearch: true,
    // },
    {
      title: '生产厂家',
      dataIndex: 'sccj',
      key: 'sccj',
      hideInSearch: true,
    },
    {
      title: '拼音代码',
      dataIndex: 'pydm',
      key: 'pydm',
      hideInTable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 150,
      render: (_, record) => [
        <a
          key="detail"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <a
          key="fix"
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        >
          修改
        </a>,
        <a
          key="delete"
          onClick={() => {
            Modal.confirm({
              title: '确认删除',
              icon: <InfoCircleOutlined />,
              content: `是否确认删除${record.ypmc}?`,
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                const success = await handleRemove(record.id);
                console.log(success)
                if (success) {
                  if (actionRef.current) {
                    actionRef.current?.reload();
                  }
                }
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  return (
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          span: 6
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          selectedRowsState?.length > 0 && (
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                Modal.confirm({
                  title: '确认重启',
                  icon: <InfoCircleOutlined />,
                  content: `是否确认批量修改这${selectedRowsState.length}种药品?`,
                  okText: '确认',
                  cancelText: '取消',
                  onOk: async () => {
                    message.info('批量修改功能正在开发中...')
                  }
                });
              }}
            >
              <EditOutlined /> 批量修改
            </Button>
          ),
          <Upload {...uProps}  key="upload">
            <Button type="primary" key="upload" icon={<UploadOutlined />}>
              导入
            </Button>
          </Upload>,
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> 新增
          </Button>,
        ]}
        params={initialState.currentUser.saID ? {
          saID: initialState.currentUser.saID
        }: {}}
        request={queryMedicineList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {createModalVisible && (
        <CreateForm
          modalVisible={createModalVisible}
          onCancel={() => handleModalVisible(false)}
          onSubmit={async (value) => {
            console.log(value)
            const success = await handleAdd({...value});
            if (success) {
              handleModalVisible(false);
              actionRef.current?.reload?.();
            }
          }}
        />
      )}
      {updateModalVisible && (
        <UpdateForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id});

            if (success) {
              handleUpdateModalVisible(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            handleUpdateModalVisible(false);
          }}
          updateModalVisible={updateModalVisible}
        />
      )}
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.ypmc && (
          <ProDescriptions
            column={1}
            title={currentRow?.ypmc}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<MEDICINE.MedicineListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default TableList;
