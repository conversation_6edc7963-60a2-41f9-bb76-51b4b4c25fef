declare namespace MEDICINE {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    type MedicineListItem = {        
        isTest?: boolean, // 是否测试
        saID?: string,      // 机构ID
        hospitalName?: string, // 机构名称
        apiVersion?: string, // 版本
        patientID?: string,    // 患者ID
        deviceCode?: number,     // 设备代码
        ypxh?: string,  // 药品序号 即条形码
        ybdm?: string,      // 药品代码
        ypmc?: string, // 药品名称
        jx?: string,     // 剂型
        gg?: string, // 总规格
        ypgg?: string, // 小计量规格
        dw?: string, // 单位
        jldw?: string,  // 计量单位
        sccj?: string,     // 生产厂家
        pydm?: string,        // 拼音代码
        createTime?: string,  // 创建时间
        state?: string,   // 状态
        id?: string,  // ID
        gytj?: string,  // 用法
        pc?: string,   // 频次
        dcyl?: string,  // 单次用量

    };
    // 医生列表
    type MedicineList = {
        /** 列表的内容 **/
        data?: MedicineListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}