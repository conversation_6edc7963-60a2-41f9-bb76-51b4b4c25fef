//报表
import { Row, Col} from 'antd'
import './index.less';

const ReportForms = ({
    data: {
        total,
    },
    date,
}) => {
   
    // const onShowSizeChange = (current, pageSize) => {
    //     console.log(current, pageSize);
    //     changeSize(current,pageSize)
    // }
    // const onChange = page => {
    //     console.log(page);
    //     changeSize(page,pageSize)
    // };
    return (
        <div className="center">
            <h1 style={{ textAlign: 'center', fontSize: '22px', lineHeight: '35px',marginBottom:0 }}><b>外配处方每日统计汇总报表</b></h1>
            <div className="search-list-row">
                <span>统计日期：</span>
                <span>{`${date[0]}至${date[1]}`}</span>
            </div>
            <Row gutter={20}>
                <Col span={24}>
                    <table className="recon-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>处方总量</th>
                                <th>有效处方</th>
                                <th>医保有效处方</th>
                                <th>自费有效处方</th>
                                <th>有效总人次</th>
                                <th>医保人次</th>
                                <th>自费人次</th>
                            </tr>
                        </thead>
                        <tbody className="scroll-tbody-wrapper">
                            {total && total.map((item, index) => {
                                return (
                                    <>
                                        <tr key={`${item.prescription_date}_1`}>
                                            <td rowSpan={1}>{item?.prescription_date}</td>
                                            <td rowSpan={1}>{item?.total_prescriptions}</td>
                                            <td rowSpan={1}>{item?.valid_prescriptions}</td>
                                            <td rowSpan={1}>{item?.medical_valid_prescriptions}</td>
                                            <td rowSpan={1}>{item?.self_pay_valid_prescriptions}</td>
                                            <td rowSpan={1}>{item?.total_person_time}</td>
                                            <td rowSpan={1}>{item?.medical_person_time}</td>
                                            <td rowSpan={1}>{item?.self_pay_person_time}</td>
                                        </tr>
                                    </>
                                )
                            })}
                        </tbody>
                    </table>
                </Col>
            </Row>
        </div>
    )
}


export default ReportForms;