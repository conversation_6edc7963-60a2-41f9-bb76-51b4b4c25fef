import { getPrescriptionStatisticsListByCreateTimeDay } from '@/services/api/reportForms';
import { parseParam } from '@/utils';
import { ProFormDateRangePicker, ProFormSelect, QueryFilter } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useModel } from '@umijs/max';
import { Card, message } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import defaultSettings from '../../../config/defaultSettings';
import ReportForms from './forms';

/**
 * 查询总数据
 * @param fields
 */
const handleQueryTotal = async (fields) => {
  try {
    const response = await getPrescriptionStatisticsListByCreateTimeDay({
      ...fields,
    });
    if (response.code === '0') {
      return response.data;
    }
    message.error(`汇总数据：${response.msg}`);
    return false;
  } catch (error) {
    message.error('查询失败请重试！');
    return false;
  }
};

/**
 * 导出Excel
 * @param fields
 */
const handleExportToExcel = async (fields) => {
  const hide = message.loading('正在导出Excel');
  const formatFields = {
    ...fields,
  };
  try {
    const paramsStr = parseParam(formatFields);
    const aLink = document.createElement('a');
    document.body.appendChild(aLink);
    aLink.style.display = 'none';
    aLink.href = `/${
      defaultSettings.apiName
    }/statement/getDailyReportExcelNew.dp?${paramsStr.substr(1)}`;
    aLink.setAttribute('download', '外配报表');
    aLink.click();
    document.body.removeChild(aLink);
    hide();
    return true;
  } catch (error) {
    hide();
    message.error('导出Excel失败请重试！');
    return false;
  }
};

const TableList = () => {
  const { hosList, fetchHosList } = useModel('hospital');
  const [dateString, setDateString] = useState<string[]>([
    moment().subtract(1, 'month').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD'),
  ]);
  const [dayData, setDayData] = useState<any>({});
  const queryLists = async (fields) => {
    const total = await handleQueryTotal(fields);
    setDayData({
      total: total,
    });
  };

  useEffect(() => {
    queryLists({
      beginTime: dateString[0],
      endTime: dateString[1],
    });
  }, []);

  const handleSearch = (values) => {
    console.log(values);
    queryLists({
      ...values,
    });
  };

  // 导出
  const handleExport = () => {};

  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 14 },
  };

  return (
    <PageContainer>
      <Card
        className="reconciliation-box"
        title={
          <QueryFilter
            defaultCollapsed
            {...formItemLayout}
            onFinish={async (values) => {
              handleSearch(values);
            }}
            style={{ padding: 0 }}
          >
            <ProFormDateRangePicker
              fieldProps={{
                defaultValue: [
                  moment(dateString[0], 'YYYY-MM-DD'),
                  moment(dateString[1], 'YYYY-MM-DD'),
                ],
                ranges: {
                  今天: [moment(), moment()],
                  昨天: [moment().subtract('1', 'days'), moment().subtract('1', 'days')],
                  一个月内: [moment().subtract('1', 'month'), moment()],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                },
              }}
              name="dateTimeRange"
              label="汇总日期"
              transform={(value) => {
                return {
                  beginTime: value[0],
                  endTime: value[1],
                };
              }}
            />
            <ProFormSelect
              name="saID"
              label="医院名称"
              fieldProps={{
                style: {
                  minWidth: 300,
                },
              }}
              request={async () => {
                const res = await fetchHosList();
                return res.filter((c) => c.value[0] === 'P');
              }}
            />
          </QueryFilter>
        }
        // extra={
        //     <Button type="primary" onClick={handleExport}>导出</Button>
        // }
      >
        <ReportForms data={dayData} date={dateString} />
      </Card>
    </PageContainer>
  );
};

export default TableList;
