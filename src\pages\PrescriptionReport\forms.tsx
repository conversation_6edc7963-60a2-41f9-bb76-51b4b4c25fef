//报表
import { Row, Col} from 'antd'
import './index.less';

const ReportForms = ({
    data: {
        store,
        total,
        doctor,
        pharmacist,
    },
    date,
}) => {
   
    // const onShowSizeChange = (current, pageSize) => {
    //     console.log(current, pageSize);
    //     changeSize(current,pageSize)
    // }
    // const onChange = page => {
    //     console.log(page);
    //     changeSize(page,pageSize)
    // };
    return (
        <div className="center">
            <h1 style={{ textAlign: 'center', fontSize: '22px', lineHeight: '35px',marginBottom:0 }}><b>外配处方报表</b></h1>
            <div className="search-list-row">
                <span>统计日期：</span>
                <span>{`${date[0]}至${date[1]}`}</span>
            </div>
            <Row gutter={20}>
                <Col span={12}>
                    <table className="recon-table">
                        <thead>
                            <tr>
                                <th colSpan={7}>接入药店汇总</th>
                            </tr>
                            <tr>
                                <th>药店名称</th>
                                <th>处方总量</th>
                                <th>有效总人次</th>
                                <th>处方类别</th>
                                <th>处方性质</th>
                                <th>数量</th>
                                <th>人次</th>
                            </tr>
                        </thead>
                        <tbody className="scroll-tbody-wrapper">
                            {store && store.map((item, index) => {
                                return (
                                    <>
                                        <tr key={`${item.saID}_1`}>
                                            <td rowSpan={3}>{item?.hospitalName}</td>
                                            <td rowSpan={3}>{item?.total_consultations}</td>
                                            <td rowSpan={3}>{item?.total_effective_person_time}</td>
                                            <td rowSpan={2}>有效处方数量</td>
                                            <td rowSpan={1}>自费</td>
                                            <td rowSpan={1}>{item?.non_medical_count}</td>
                                            <td rowSpan={1}>{item?.non_medical_effective_person_time}</td>
                                        </tr>
                                        <tr key={`${item.saID}_2`}>
                                            <td rowSpan={1}>医保</td>
                                            <td rowSpan={1}>{item?.medical_count}</td>
                                            <td rowSpan={1}>{item?.medical_effective_person_time}</td>
                                        </tr>
                                        <tr key={`${item.saID}_3`}>
                                            <td rowSpan={1}>无效处方数量</td>
                                            <td rowSpan={1}>退号</td>
                                            <td rowSpan={1}>{item?.refund_count}</td>
                                            <td rowSpan={1}>{item?.refund_person_time}</td>
                                        </tr>
                                    </>
                                )
                            })}
                        </tbody>
                    </table>
                </Col>
                <Col span={12}>
                    <Row gutter={[20, 20]}>
                        <Col span={24}>
                            <table className="recon-table">
                                <thead>
                                    <tr>
                                        <th colSpan={7}>接入医院汇总</th>
                                    </tr>
                                    <tr>
                                        <th>药店名称</th>
                                        <th>处方总量</th>
                                        <th>有效总人次</th>
                                        <th>处方类别</th>
                                        <th>处方性质</th>
                                        <th>数量</th>
                                        <th>人次</th>
                                    </tr>
                                </thead>
                                <tbody className="scroll-tbody-wrapper">
                                    {total && total.map((item, index) => {
                                        return (
                                            <>
                                                <tr key={`${item.saID}_1`}>
                                                    <td rowSpan={3}>{item?.hospitalName}</td>
                                                    <td rowSpan={3}>{item?.total_consultations}</td>
                                                    <td rowSpan={3}>{item?.total_effective_person_time}</td>
                                                    <td rowSpan={2}>有效处方数量</td>
                                                    <td rowSpan={1}>自费</td>
                                                    <td rowSpan={1}>{item?.non_medical_count}</td>
                                                    <td rowSpan={1}>{item?.non_medical_effective_person_time}</td>
                                                </tr>
                                                <tr key={`${item.saID}_2`}>
                                                    <td rowSpan={1}>医保</td>
                                                    <td rowSpan={1}>{item?.medical_count}</td>
                                                    <td rowSpan={1}>{item?.medical_effective_person_time}</td>
                                                </tr>
                                                <tr key={`${item.saID}_3`}>
                                                    <td rowSpan={1}>无效处方数量</td>
                                                    <td rowSpan={1}>退号</td>
                                                    <td rowSpan={1}>{item?.refund_count}</td>
                                                    <td rowSpan={1}>{item?.refund_person_time}</td>
                                                </tr>
                                            </>
                                        )
                                    })}
                                </tbody>
                            </table>
                        </Col>
                        <Col span={12}>
                            <table className="recon-table">
                                <thead>
                                    <tr>
                                        <th colSpan={3}>医院医生工作量</th>
                                    </tr>
                                    <tr>
                                        <th>医生姓名</th>
                                        <th>有效处方数量</th>
                                        <th>人次</th>
                                    </tr>
                                </thead>
                                <tbody className="scroll-tbody-wrapper">
                                    {doctor && doctor.map((item, index) => {
                                        return (
                                            <>
                                                <tr key={`${item.doctor_name}_1`}>
                                                    <td rowSpan={1}>{item?.doctor_name}</td>
                                                    <td rowSpan={1}>{item?.total_prescriptions}</td>
                                                    <td rowSpan={1}>{item?.person_time}</td>
                                                </tr>
                                            </>
                                        )
                                    })}
                                </tbody>
                            </table>
                        </Col>
                        <Col span={12}>
                            <table className="recon-table">
                                <thead>
                                    <tr>
                                        <th colSpan={3}>药店审方药师工作量</th>
                                    </tr>
                                    <tr>
                                        <th>药师姓名</th>
                                        <th>药店名称</th>
                                        <th>有效处方数量</th>
                                    </tr>
                                </thead>
                                <tbody className="scroll-tbody-wrapper">
                                    {pharmacist && pharmacist.map((item, index) => {
                                        return (
                                            <>
                                                <tr key={`${item.pharmacist_name}_1`}>
                                                    <td rowSpan={1}>{item?.pharmacist_name}</td>
                                                    <td rowSpan={1}>{item?.hospitalName}</td>
                                                    <td rowSpan={1}>{item?.total_prescriptions}</td>
                                                </tr>
                                            </>
                                        )
                                    })}
                                </tbody>
                            </table>
                        </Col>
                    </Row>
                </Col>
                
            </Row>
        </div>
    )
}


export default ReportForms;