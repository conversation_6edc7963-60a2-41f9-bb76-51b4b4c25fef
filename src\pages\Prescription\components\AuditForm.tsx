import { ModalForm, ProFormTextArea, ProFormSelect} from '@ant-design/pro-form';
import { Button, Modal} from 'antd';
import {
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<PRESCRIPTION.ItemListItem>;

export type UploadFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values: FormValueType;
};

const AuditForm: React.FC<UploadFormProps> = ({ modalVisible, onCancel, onSubmit, values}) => {

  const { fetchDocList } = useModel('hospital');
  const { initialState } = useModel('@@initialState');  

  return (
    <ModalForm
      title="药师签名"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        okText: '确认',
        cancelText: '关闭',
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            <a key="skip" onClick={() => {
              Modal.confirm({
                title: '确认跳过审核',
                icon: <InfoCircleOutlined />,
                content: `是否确认跳过该处方审核?`,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  onSubmit({
                    checkState: '1',
                  })
                }
              });
            }} >
              跳过签名，转线下签字
            </a>,
            ...defaultDoms,
            // <Button
            //   key="noPass"
            //   onClick={() => {
            //     onSubmit({
            //       checkState: '2',
            //     })
            //   }}
            // >
            //   审核不通过
            // </Button>,
          ];
        },
      }}
      onFinish={(fields) => {
        return onSubmit({
          doubleCheckDoctor: fields.doubleCheckDoctor?.split('-')[0],
          checkPharmacist: fields.checkPharmacist?.split('-')[0],
          dispensingDoctor: fields.dispensingDoctor?.split('-')[0],
          checkState: '1',
        })
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      <ProFormSelect
        name="checkPharmacist"
        label="审核药师"
        fieldProps={{
          showSearch: true,
          optionFilterProp: "label"
        }}
        request={() => fetchDocList({
          saID: initialState.currentUser.saID
        })}
        rules={[
          {
            required: true,
            message: '审核药师为必填项',
          },
        ]}
      />
      <ProFormSelect
        name="doubleCheckDoctor"
        label="调配、复核"
        fieldProps={{
          showSearch: true,
          optionFilterProp: "label"
        }}
        request={() => fetchDocList({
          saID: initialState.currentUser.saID
        })}
        rules={[
          {
            required: true,
            message: '调配、复核为必填项',
          },
        ]}
      />
      <ProFormSelect
        name="dispensingDoctor"
        label="核对、发药"
        fieldProps={{
          showSearch: true,
          optionFilterProp: "label"
        }}
        request={() => fetchDocList({
          saID: initialState.currentUser.saID
        })}
        rules={[
          {
            required: true,
            message: '核对、发药为必填项',
          },
        ]}
      />
    </ModalForm>
  );
};

export default AuditForm;