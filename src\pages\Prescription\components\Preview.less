/* 全局样式 */
// * {
//   box-sizing: border-box;
//   margin: 0;
//   padding: 0;
//   font-family: 'SimSun', '宋体', serif;
// }

.container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #2c3e50;
  position: fixed;
  top: -35px;
  right: 20px;
  z-index: 1000;
}

h1 {
  text-align: center;
  margin-bottom: 20px;
  color: #FFF;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.print-btn:hover, .export-btn:hover {
  background-color: #2980b9;
}

/* 处方单样式 */
.prescription-container {
  height: 950px;
  background-color: white;
  border: 1px solid #ddd;
  padding: 30px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  font-family: 'SimHei', '黑体', serif;
}

.hospital-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  // border-bottom: 2px solid #e74c3c;
  position: relative;
  .hospital-name {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
  }

  .prescription-type {
    font-size: 18px;
    // color: #e74c3c;
  }

  .no-header{
    position: absolute;
    bottom: -10px;
    right: 30px;
    .no-text{
      font-size: 20px;
      font-weight: bold;
      color: #e74c3c;
      margin-left: 30px;
    }
    .number{
      font-size: 16px;
      font-weight: bold;
      color: #e74c3c;
    }
  }

  .tags{
    position: absolute;
    top: -10px;
    right: 20px;
    width: 100px;
    height: 64px;
    border: 4px solid #000;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
  }

  .barcode-section {
    position: absolute;
    top: -10px;
    left: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;

    .barcode-text {
      font-size: 12px;
      color: #000;
      margin-top: 5px;
      text-align: center;
      font-weight: bold;
    }
  }
}

.prescription-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  font-size: 14px;
}

.prescription-info > div {
  display: flex;
  flex-direction: column;
}

.prescription-info span {
  font-weight: bold;
  margin-bottom: 3px;
}

.patient-info {
  margin-bottom: 20px;
}

.medical-no{
  position: absolute;
  bottom: -10px;
  left: 0px;
  .info-item {
    margin-right: 15px;
    font-weight: bold;
    font-size: 14px;
    span {
      font-weight: bold;
    }
  }
}

.info-row1 {
  display: flex;
  margin-bottom: 10px;
  .info-item {
    margin-right: 15px;
    &.name{
      width: 200px;
    }
    &.sex, &.age{
      width: 80px;
    }
    &.idCard{
      flex: 1
    }
    span {
      font-weight: bold;
    }
  }
}

.info-row2 {
  display: flex;
  margin-bottom: 10px;
  .info-item {
    margin-right: 15px;
    &.patientNo{
      width: 200px;
    }
    &.dept{
      width: 175px;
    }
    &.predate{
      width: 200px;
    }
    &.phone{
      width: 200px;
    }
    &.address{
      flex: 1
    }
    span {
      font-weight: bold;
    }
  }
}

.diagnosis {
  margin-bottom: 20px;
  font-size: 16px;
  span {
    font-weight: bold;
  }
}

.date{
  margin-bottom: 5px;
  font-size: 12px;
  span {
    font-weight: bold;
  }
}

.prescription-content {
  min-height: 380px;
  position: relative;
}

.rp-header {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 2px;
  color: #e74c3c;
}

.medication {
  margin: 0  0 15px 40px;
}

.med-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.med-usage {
  padding-left: 20px;
}

.doctor-info {
  padding-top: 5px !important;
  border-top: 2px solid #000 !important;
  border-bottom: 2px solid #000 !important;
}

.doctor-row {
  display: flex;
  margin-bottom: 10px;
}

.doctor-item {
  flex: 1;
  .sign-image{
    width: 80px;
    height: 35px;
  }
}

.doctor-item span {
  font-weight: bold;
}

.stamp {
  position: absolute;
  bottom: 30px;
  right: 30px;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  padding: 10px;
  transform: rotate(-15deg);
}

.notice-section{
  margin-top: 0px;
}

/* 打印样式 */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    text-rendering: optimizeLegibility !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  .container {
    position: static !important;
    background-color: white !important;
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
    z-index: auto !important;
  }

  .title, .controls {
    display: none !important;
  }

  .prescription-container {
    box-shadow: none !important;
    border: none !important;
    padding: 20mm !important;
    width: 210mm !important;
    height: 297mm !important;
    margin: 0 auto !important;
    background-color: white !important;
    font-family: 'SimHei', '黑体', serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
    color: #000 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  .hospital-header {
    text-align: center !important;

    .hospital-name {
      font-size: 28px !important;
      font-weight: bold !important;
      color: #000 !important;
      margin-bottom: 8px !important;
    }

    .prescription-type {
      font-size: 20px !important;
      color: #000 !important;
      font-weight: bold !important;
    }

    .no-header {
      .no-text {
        font-size: 18px !important;
        font-weight: bold !important;
        color: #000 !important;
      }
      .number {
        font-size: 16px !important;
        font-weight: bold !important;
        color: #000 !important;
      }
    }

    .tags {
      border: 3px solid #000 !important;
      font-size: 18px !important;
      font-weight: bold !important;
      color: #000 !important;
    }

    /* 条形码打印样式 - 左上角绝对定位 */
    .barcode-section {
      position: absolute !important;
      top: -10px !important;
      left: -20px !important;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      z-index: 10 !important;

      .barcode-text {
        font-size: 12px !important;
        color: #000 !important;
        margin-top: 5px !important;
        text-align: center !important;
        font-weight: bold !important;
      }
    }

    .medical-no{
      .info-item {
        font-weight: bold;
        font-size: 16px !important;
        span {
          font-weight: bold !important;
        }
      }
    }
  }

  .patient-info {
    .info-row1, .info-row2 {
      .info-item {
        font-size: 16px !important;
        color: #000 !important;
        span {
          font-weight: bold !important;
          color: #000 !important;
        }
      }
    }

    .diagnosis {
      font-size: 16px !important;
      color: #000 !important;
      span {
        font-weight: bold !important;
        color: #000 !important;
      }
    }

    .date {
      font-size: 14px !important;
      color: #000 !important;
      span {
        font-weight: bold !important;
        color: #000 !important;
      }
    }
  }

  .prescription-content {
    .rp-header {
      font-size: 22px !important;
      font-weight: bold !important;
      color: #000 !important;
    }

    .medication {
      .med-name {
        font-size: 16px !important;
        font-weight: bold !important;
        color: #000 !important;
        margin-bottom: 6px !important;
      }

      .med-usage {
        font-size: 14px !important;
        color: #000 !important;
        padding-left: 20px !important;
      }
    }

  }

  .doctor-info {
    border-top: 2px solid #000 !important;
    padding-top: 15px !important;
    font-size: 16px !important;

    .doctor-item {
      font-size: 16px !important; /* 增大打印时医生信息字号 */
      color: #000 !important;
      span {
        font-weight: bold !important;
        color: #000 !important;
      }

      .sign-image {
        width: 80px !important;
        height: 35px !important;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
      }
    }
  }

  /* 注意事项打印样式 */
  .notice-section {
    .notice-divider {
      border-top: 2px solid #000 !important;
      margin-bottom: 10px !important;
    }

    .notice-text {
      font-size: 12px !important;
      line-height: 1.5 !important;
      color: #000 !important;
      text-align: justify !important;
      padding: 0 10px !important;
    }
  }
}
