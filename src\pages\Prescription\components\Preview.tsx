import { useRef } from 'react';
import { RedoOutlined, PrinterOutlined, FilePdfOutlined, PictureOutlined } from '@ant-design/icons';
import { useReactToPrint } from 'react-to-print';
import { But<PERSON>, Image} from 'antd';
import html2canvas from 'html2canvas';
import Draggable from 'react-draggable'
import jsPDF from 'jspdf';
import { formatIdCard, getServerFilesHost, IdNoPrivate} from '@/utils';
import dayjs from 'dayjs';
import Barcode from 'react-barcode';
import './Preview.less';

const PrescriptionApp = ({ onClosed, values, onHandlePrint, onHandleRefresh}) => {
  const prescriptionRef = useRef<HTMLDivElement>(null);
  
  
  // 打印功能
  const handlePrint = useReactToPrint({
    contentRef: prescriptionRef,
    documentTitle: `处方单__${values.patientInfo.patientName}_${dayjs(values.createTime).format('YYYY-MM-DD')}`,
  });

  // 导出PDF功能
  const exportPDF = () => {
    const input = prescriptionRef.current;

    if (!input) {
      console.error('处方单元素未找到');
      return;
    }

    // 等待一段时间确保图片加载完成
    setTimeout(() => {
      html2canvas(input, {
        scale: 3, // 提高分辨率
        useCORS: true,
        allowTaint: true, // 允许跨域图片
        logging: false,
        backgroundColor: '#ffffff',
        imageTimeout: 15000, // 图片加载超时时间
        removeContainer: true,
        foreignObjectRendering: false, // 禁用外部对象渲染，提高兼容性
        onclone: (clonedDoc) => {
          // 在克隆的文档中处理 Ant Design Image 组件
          const antImages = clonedDoc.querySelectorAll('.ant-image img');
          antImages.forEach((img) => {
            const htmlImg = img as HTMLImageElement;
            htmlImg.style.display = 'block';
            htmlImg.style.visibility = 'visible';
            htmlImg.style.opacity = '1';
          });
        }
      }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const imgWidth = 210; // A4宽度210mm
        const imgHeight = canvas.height * imgWidth / canvas.width;

        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
        pdf.save(`处方单_${values.patientInfo.patientName}_${dayjs(values.createTime).format('YYYY-MM-DD')}.pdf`);
      }).catch(error => {
        console.error('导出PDF失败:', error);
      });
    }, 1000); // 等待1秒确保图片加载完成
  };

  // 导出图片功能
  const exportImage = () => {
    const input = prescriptionRef.current;
    if (!input) {
      console.error('处方单元素未找到');
      return;
    }
    
    // 等待一段时间确保图片加载完成
    setTimeout(() => {
      html2canvas(input, {
        scale: 3, // 高分辨率
        useCORS: true,
        allowTaint: true, // 允许跨域图片
        logging: false,
        backgroundColor: '#ffffff',
        width: input.offsetWidth,
        height: input.offsetHeight,
        imageTimeout: 15000, // 图片加载超时时间
        removeContainer: true,
        foreignObjectRendering: false, // 禁用外部对象渲染，提高兼容性
        onclone: (clonedDoc) => {
          // 在克隆的文档中处理 Ant Design Image 组件
          const antImages = clonedDoc.querySelectorAll('.ant-image img');
          antImages.forEach((img) => {
            const htmlImg = img as HTMLImageElement;
            htmlImg.style.display = 'block';
            htmlImg.style.visibility = 'visible';
            htmlImg.style.opacity = '1';
          });
        }
      }).then(canvas => {
        // 创建下载链接
        const link = document.createElement('a');
        link.download = `处方单_${values.patientInfo.patientName}_${dayjs(values.createTime).format('YYYY-MM-DD')}.png`;
        link.href = canvas.toDataURL('image/png', 1.0); // 最高质量
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }).catch(error => {
        console.error('导出图片失败:', error);
      });
    }, 1000); // 等待1秒确保图片加载完成
  };

  return (
    <Draggable>
      <div className="container">
        <h1 className='title'>处方单预览系统</h1>

        <div className="controls">
          <Button type='primary' onClick={() => {
            onHandleRefresh()
          }} size='large'><RedoOutlined />刷新数据</Button>
          <Button type='primary' onClick={async () => { 
            const res = await onHandlePrint(); 
            console.log(res)
            if(!res) return;
            handlePrint() 
          }} size='large'><PrinterOutlined />打印处方单</Button>
          <Button type='primary' onClick={async () => { 
            const res = await onHandlePrint(); 
            if(!res) return;
            exportPDF(); 
          }} size='large'><FilePdfOutlined />导出PDF</Button>
          <Button type='primary' onClick={async () => { 
            const res = await onHandlePrint(); 
            if(!res) return;
            exportImage();
          }} size='large'><PictureOutlined />导出图片</Button>
          <Button type='primary' danger onClick={() => onClosed()} size='large'>关闭预览</Button>
        </div>
        
        <div ref={prescriptionRef} className="prescription-container">
          {/* 医院信息 */}
          <div className="hospital-header">
            <div className="hospital-name">{'杭州市临安区第三人民医院'}</div>
            <div className="prescription-type">{'外配处方'}</div>
            <div className="no-header"><span className='no-text'>No：</span><span className="number">{ values.circulationNumber }</span></div>
            <div className="tags">普通门诊 外配处方</div>
            {/* 条形码区域 - 左上角绝对定位 */}
            {values.prescriptionNo && (
              <div className="barcode-section">
                <div className="barcode-text">处方编号：{values.prescriptionNo}</div>
                <Barcode
                  value={values.prescriptionNo}
                  format="CODE128"
                  width={1.2}
                  height={40}
                  displayValue={false}
                  fontSize={12}
                  margin={0}
                />
              </div>
            )}
            { values.recordNumber && <div className="medical-no">
              <div className="info-item">
                <span>备案编号：</span>
                {values.recordNumber}
              </div>
            </div> }
          </div>
          {/* 患者信息 */}
          <div className="patient-info">
            <div className="info-row1">
              <div className="info-item name">
                <span>姓名：</span>
                {values.patientInfo.patientName}
              </div>
              <div className="info-item sex">
                <span>性别：</span>
                {formatIdCard(values.patientInfo.idCard, 2)}
              </div>
              <div className="info-item age">
                <span>年龄：</span>
                {formatIdCard(values.patientInfo.idCard, 3)}
              </div>
              <div className="info-item idCard">
                <span>证件号：</span>
                {IdNoPrivate(values.patientInfo.idCard)}
              </div>
            </div>
            <div className="info-row2">
              <div className="info-item patientNo">
                <span>病案号：</span>
                {values.patientInfo.patientID}
              </div>
              <div className="info-item dept">
                <span>科别：</span>
                {'外配处方科室'}
              </div>
              <div className="info-item predate">
                <span>开方日期：</span>
                {dayjs(values.createTime).format('YYYY年MM月DD日')}
              </div>
            </div>
            <div className="info-row2">
              <div className="info-item phone">
                <span>电话：</span>
                {values.patientInfo.patientPhone ?? ""}
              </div>
              <div className="info-item address">
                <span>地址（单位）：</span>
                {values.patientInfo.permanentAddress ?? ""}
              </div>
            </div>
            <div className="info-row">
              <div className="diagnosis">
                <span>临床（初步）诊断：</span>
                {values.diagnosis}
              </div>
            </div>
            {/* <div className="info-row">
              <div className="date">
                <span>开方日期：</span>
                {dayjs(values.createTime).format('YYYY-MM-DD')}
              </div>
            </div> */}
          </div>
          {/* 处方内容 */}
          <div className="prescription-content">
            <div className="rp-header">Rp</div>
            <div className="medications">
              {values.prescriptionInfoJson.map((med, index) => (
                <div key={index} className="medication">
                  <div className="med-name">{med.ypmc} {med.gg} × {med.sl}{med.dw || ""}</div>
                  <div className="med-usage">用法用量：{med.yf}&nbsp;{med.yfyl}</div>
                </div>
              ))}
            </div>
            {/* 印章 */}
            <div className="stamp">
              <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/处方章.png`} fallback={require('@/assets/white.png')} preview={false}/> 
            </div>
          </div>
          
          {/* 医师信息 */}
          <div className="doctor-info">
            <div className="doctor-row">
              <div className="doctor-item">
                <span>开方医师：</span>
                {values.prescriptionDoctor}
                { values.prescriptionDoctor && <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/${values.prescriptionDoctor}.png`} fallback={require('@/assets/white.png')}/> }
              </div>
              <div className="doctor-item">
                <span>药师：</span>
                {values.pharmacist ? values.pharmacist : '竺群'}
                { values.pharmacist ? <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/${values.pharmacist}.png`} fallback={require('@/assets/white.png')}/> : 
                <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/竺群.png`} fallback={require('@/assets/white.png')}/> }
              </div>
            </div>
            <div className="doctor-row">
              <div className="doctor-item">
                <span>审核药师：</span>
                { values.checkPharmacist }
                { values.checkPharmacist && <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/${values.saID}${values.checkPharmacist}.png`} fallback={require('@/assets/white.png')}/>}
              </div>
              <div className="doctor-item">
                <span>调配、复核：</span>
                { values.doubleCheckDoctor }
                { values.doubleCheckDoctor && <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/${values.saID}${values.doubleCheckDoctor}.png`} fallback={require('@/assets/white.png')}/> }
              </div>
            </div>
            <div className="doctor-row">
              <div className="doctor-item">
                <span>核对、发药：</span>
                { values.dispensingDoctor }
                { values.dispensingDoctor && <Image className='sign-image' src={`${getServerFilesHost()}doctorsign/${values.saID}${values.dispensingDoctor}.png`} fallback={require('@/assets/white.png')}/> }
              </div>
            </div>
          </div>

          {/* 医生签名后的分割线和注意事项 */}
          <div className="notice-section">
            <div className="notice-text">
              为了保证患者用药安全，有效，请注意：1.认真核对药名和数量等处方信息 2.严禁将本处方药品予他人使用 3.药品一经发出，不得退换 4.本处方3日内有效，过期作废
            </div>
          </div>
        </div>
      </div>
    </Draggable>
  );
};

export default PrescriptionApp;
