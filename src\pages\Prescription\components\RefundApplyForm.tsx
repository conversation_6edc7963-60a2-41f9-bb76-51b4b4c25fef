import { ModalForm, ProFormTextArea, ProFormSelect, ProFormText} from '@ant-design/pro-form';
import { Button, Modal} from 'antd';
import {
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import MD5 from 'js-md5';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<PRESCRIPTION.ItemListItem>;

export type RefundApplyFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values: FormValueType;
};

const RefundApplyForm: React.FC<RefundApplyFormProps> = ({ modalVisible, onCancel, onSubmit, values}) => {

  const { fetchDicList } = useModel('dictionary');

  return (
    <ModalForm
      title="退号申请"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        okText: '确认',
        cancelText: '关闭',
      }}
      initialValues={{
        ...values,
        invoice: values.fatherInvoice || values.invoice,
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            ...defaultDoms,
          ];
        },
      }}
      onFinish={(fields) => {
        return onSubmit({
          ...fields,
          refundPwd: MD5(fields.refundPwd).toUpperCase()
        })
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      { values.fatherInvoice && <ProFormText
        name="tips"
        label="提醒"
        readonly
      />}
      <ProFormText
        name="invoice"
        label="流水号"
        rules={[
          {
            required: true,
            message: '流水号为必填项',
          },
        ]}
        hidden
      />
      <ProFormSelect
        name="refundReason"
        label="申请原因"
        rules={[
          {
            required: true,
            message: '申请原因为必填项',
          },
        ]}
        request={() => fetchDicList('ApplyRefundReason')}  
      />
    </ModalForm>
  );
};

export default RefundApplyForm;