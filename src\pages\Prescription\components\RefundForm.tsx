import { ModalForm, ProFormTextArea, ProFormSelect, ProFormText} from '@ant-design/pro-form';
import { Button, Modal} from 'antd';
import {
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import MD5 from 'js-md5';
import 'moment/locale/zh-cn';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 14 },
}

type FormValueType = Partial<PRESCRIPTION.ItemListItem>;

export type RefundFormProps = {
  onCancel: (flag?: boolean, formVals?: FormValueType) => void;
  onSubmit: (values: FormValueType) => Promise<void>;
  modalVisible: boolean;
  values: FormValueType;
};

const RefundForm: React.FC<RefundFormProps> = ({ modalVisible, onCancel, onSubmit, values}) => {

  return (
    <ModalForm
      title="处方退号"
      layout="horizontal"
      width={640}
      open={modalVisible}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => onCancel(),
        maskClosable: false,
        okText: '确认',
        cancelText: '关闭',
      }}
      initialValues={{
        ...values,
        invoice: values.fatherInvoice || values.invoice,
        tips: values.fatherInvoice ? '本次退费将退掉患者当日首次问诊产生的挂号费': ""
      }}
      submitter={{
        render: (props, defaultDoms) => {
          return [
            ...defaultDoms,
          ];
        },
      }}
      onFinish={(fields) => {
        return onSubmit({
          ...fields,
          refundPwd: MD5(fields.refundPwd).toUpperCase()
        })
      }}
      {...formItemLayout}
      className="_modal-wrapper"
    >
      { values.fatherInvoice && <ProFormText
        name="tips"
        label="提醒"
        readonly
      />}
      <ProFormText
        name="invoice"
        label="流水号"
        rules={[
          {
            required: true,
            message: '流水号为必填项',
          },
        ]}
        hidden
      />
      <ProFormText
        name="refundOpterID"
        label="操作员工号"
        rules={[
          {
            required: true,
            message: '工号为必填项',
          },
        ]}
      />
      <ProFormText
        name="refundOpterName"
        label="操作员姓名"
        rules={[
          {
            required: true,
            message: '姓名为必填项',
          },
        ]}
      />
      <ProFormText.Password
        name="refundPwd"
        label="退款密钥"
        rules={[
          {
            required: true,
            message: '退款密钥为必填项',
          },
        ]}
      />
    </ModalForm>
  );
};

export default RefundForm;