/**
 * WebSocket测试组件
 * 用于测试处方管理系统的WebSocket通知功能
 */

import React, { useState, useRef } from 'react';
import { Button, Card, Input, Select, Space, Typography, Divider, message } from 'antd';
import { SendOutlined, DisconnectOutlined, ApiOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface TestMessage {
  type: 'new_prescription' | 'prescription_audit' | 'prescription_refund' | 'prescription_urgent' | 'system_message';
  patientName?: string;
  prescriptionNo?: string;
  status?: string;
  amount?: number;
  message?: string;
}

const WebSocketTest: React.FC = () => {
  const [connected, setConnected] = useState(false);
  const [messageType, setMessageType] = useState<TestMessage['type']>('new_prescription');
  const [patientName, setPatientName] = useState('张三');
  const [prescriptionNo, setPrescriptionNo] = useState('CF202312250001');
  const [status, setStatus] = useState('审核通过');
  const [amount, setAmount] = useState(158.5);
  const [customMessage, setCustomMessage] = useState('这是一条测试消息');
  const wsRef = useRef<WebSocket | null>(null);

  // 连接WebSocket
  const connectWebSocket = () => {
    try {
      const wsUrl = process.env.NODE_ENV === 'development' 
        ? 'ws://localhost:8080/prescription-notifications' 
        : `ws://${window.location.host}/prescription-notifications`;
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        setConnected(true);
        message.success('WebSocket连接成功');
      };
      
      wsRef.current.onclose = () => {
        setConnected(false);
        message.info('WebSocket连接已关闭');
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        message.error('WebSocket连接失败');
      };
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      message.error('创建WebSocket连接失败');
    }
  };

  // 断开WebSocket
  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  // 发送测试消息
  const sendTestMessage = () => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      message.error('WebSocket未连接');
      return;
    }

    let testMessage: TestMessage = { type: messageType };

    switch (messageType) {
      case 'new_prescription':
        testMessage = {
          type: 'new_prescription',
          patientName,
          prescriptionNo,
        };
        break;
      case 'prescription_audit':
        testMessage = {
          type: 'prescription_audit',
          prescriptionNo,
          status,
        };
        break;
      case 'prescription_refund':
        testMessage = {
          type: 'prescription_refund',
          prescriptionNo,
          amount,
        };
        break;
      case 'prescription_urgent':
        testMessage = {
          type: 'prescription_urgent',
          patientName,
          prescriptionNo,
        };
        break;
      case 'system_message':
        testMessage = {
          type: 'system_message',
          message: customMessage,
        };
        break;
    }

    try {
      wsRef.current.send(JSON.stringify(testMessage));
      message.success('测试消息已发送');
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    }
  };

  // 预设测试消息
  const sendPresetMessage = (preset: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      message.error('WebSocket未连接');
      return;
    }

    let presetMessage: TestMessage;

    switch (preset) {
      case 'newPrescription':
        presetMessage = {
          type: 'new_prescription',
          patientName: '李四',
          prescriptionNo: 'CF202312250002',
        };
        break;
      case 'auditPass':
        presetMessage = {
          type: 'prescription_audit',
          prescriptionNo: 'CF202312250003',
          status: '审核通过',
        };
        break;
      case 'auditReject':
        presetMessage = {
          type: 'prescription_audit',
          prescriptionNo: 'CF202312250004',
          status: '审核不通过',
        };
        break;
      case 'refund':
        presetMessage = {
          type: 'prescription_refund',
          prescriptionNo: 'CF202312250005',
          amount: 299.8,
        };
        break;
      case 'urgent':
        presetMessage = {
          type: 'prescription_urgent',
          patientName: '王五',
          prescriptionNo: 'CF202312250006',
        };
        break;
      default:
        return;
    }

    try {
      wsRef.current.send(JSON.stringify(presetMessage));
      message.success(`预设消息"${preset}"已发送`);
    } catch (error) {
      console.error('发送预设消息失败:', error);
      message.error('发送预设消息失败');
    }
  };

  return (
    <Card title="WebSocket通知测试工具" style={{ margin: '20px' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 连接控制 */}
        <div>
          <Title level={4}>连接控制</Title>
          <Space>
            <Button 
              type="primary" 
              icon={<ApiOutlined />}
              onClick={connectWebSocket}
              disabled={connected}
            >
              连接WebSocket
            </Button>
            <Button 
              danger 
              icon={<DisconnectOutlined />}
              onClick={disconnectWebSocket}
              disabled={!connected}
            >
              断开连接
            </Button>
            <Text type={connected ? 'success' : 'secondary'}>
              状态: {connected ? '已连接' : '未连接'}
            </Text>
          </Space>
        </div>

        <Divider />

        {/* 自定义消息 */}
        <div>
          <Title level={4}>自定义测试消息</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text>消息类型：</Text>
              <Select 
                value={messageType} 
                onChange={setMessageType}
                style={{ width: 200, marginLeft: 8 }}
              >
                <Option value="new_prescription">新处方通知</Option>
                <Option value="prescription_audit">处方审核通知</Option>
                <Option value="prescription_refund">处方退费通知</Option>
                <Option value="prescription_urgent">紧急处方通知</Option>
                <Option value="system_message">系统消息</Option>
              </Select>
            </div>

            {(messageType === 'new_prescription' || messageType === 'prescription_urgent') && (
              <div>
                <Text>患者姓名：</Text>
                <Input 
                  value={patientName} 
                  onChange={(e) => setPatientName(e.target.value)}
                  style={{ width: 200, marginLeft: 8 }}
                />
              </div>
            )}

            {messageType !== 'system_message' && (
              <div>
                <Text>处方编号：</Text>
                <Input 
                  value={prescriptionNo} 
                  onChange={(e) => setPrescriptionNo(e.target.value)}
                  style={{ width: 200, marginLeft: 8 }}
                />
              </div>
            )}

            {messageType === 'prescription_audit' && (
              <div>
                <Text>审核状态：</Text>
                <Select 
                  value={status} 
                  onChange={setStatus}
                  style={{ width: 200, marginLeft: 8 }}
                >
                  <Option value="审核通过">审核通过</Option>
                  <Option value="审核不通过">审核不通过</Option>
                  <Option value="待审核">待审核</Option>
                </Select>
              </div>
            )}

            {messageType === 'prescription_refund' && (
              <div>
                <Text>退费金额：</Text>
                <Input 
                  type="number"
                  value={amount} 
                  onChange={(e) => setAmount(Number(e.target.value))}
                  style={{ width: 200, marginLeft: 8 }}
                  addonBefore="¥"
                />
              </div>
            )}

            {messageType === 'system_message' && (
              <div>
                <Text>消息内容：</Text>
                <TextArea 
                  value={customMessage} 
                  onChange={(e) => setCustomMessage(e.target.value)}
                  rows={3}
                  style={{ marginLeft: 8, width: 'calc(100% - 80px)' }}
                />
              </div>
            )}

            <Button 
              type="primary" 
              icon={<SendOutlined />}
              onClick={sendTestMessage}
              disabled={!connected}
            >
              发送测试消息
            </Button>
          </Space>
        </div>

        <Divider />

        {/* 预设消息 */}
        <div>
          <Title level={4}>预设测试消息</Title>
          <Space wrap>
            <Button onClick={() => sendPresetMessage('newPrescription')} disabled={!connected}>
              新处方通知
            </Button>
            <Button onClick={() => sendPresetMessage('auditPass')} disabled={!connected}>
              审核通过
            </Button>
            <Button onClick={() => sendPresetMessage('auditReject')} disabled={!connected}>
              审核不通过
            </Button>
            <Button onClick={() => sendPresetMessage('refund')} disabled={!connected}>
              处方退费
            </Button>
            <Button onClick={() => sendPresetMessage('urgent')} disabled={!connected}>
              紧急处方
            </Button>
          </Space>
        </div>
      </Space>
    </Card>
  );
};

export default WebSocketTest;
