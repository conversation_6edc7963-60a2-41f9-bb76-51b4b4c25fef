
import { But<PERSON>, Modal, Drawer, Select, Image, message, Space, notification} from 'antd';
import { useState, useRef, useEffect, useMemo, createRef, Fragment} from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import { queryPrescriptionList, updatePrescription, queryPrescriptionInfo} from '@/services/api/hospital';
import { refundPrescription } from '@/services/api/logs';
import { useModel } from '@umijs/max';
import ProTable from '@ant-design/pro-table';
import ProDescriptions from '@ant-design/pro-descriptions';
import AuditForm from './components/AuditForm';
import RefundForm from './components/RefundForm';
import Preview from './components/Preview';
import WebSocketTest from './components/WebSocketTest';
import dayjs from 'dayjs'
import { handleOperateMethod } from '@/utils/index';
import { formatIdCard } from '@/utils';
import styles from './index.less'

const { Option } = Select

export type ListType = {
  value?: any;
  label?: string;
};

/**
 * @zh-CN 修改处方
 * @param fields
 */
const handleUpdate = (fields: PRESCRIPTION.ItemListItem) => handleOperateMethod(updatePrescription, fields, 'default', '请求中', '操作成功', '操作失败');

/**
 * @zh-CN 查询处方详情信息
 * @param fields
 */
const handlePrescriptionInfo = async (fields: PRESCRIPTION.ItemListItem) => {
  const hide = message.loading('信息加载中...');
  try {
    const response = await queryPrescriptionInfo({ ...fields });
    hide()
    if (response.code === '0') {
      return response.data;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('获取处方信息失败！');
    return false;
  }
};

/**
 * @zh-CN 处方退费
 * @param fields
 */
const handleRefund = async (fields: PRESCRIPTION.ItemListItem) =>{
  const hide = message.loading('信息查询中...');
  try {
    const response = await refundPrescription({ ...fields });
    hide()
    if (response.code === '0') {
      message.success('退费成功');
      return true;
    }
    message.error(response.msg);
    return false;
  } catch (error) {
    hide();
    message.error('退费失败请重试！');
    return false;
  }
}

const TableList = () => {
  const { initialState } = useModel('@@initialState');
  /** 展示开单详情的弹窗 */
  const [showDetail, setShowDetail] = useState(false);
  const actionRef = useRef<ActionType>();
  /** 取当前行数据 */
  const [currentRow, setCurrentRow] = useState<PRESCRIPTION.ItemListItem>();
  const [showPreview, setShowPreview] = useState(false);
  // 审核弹窗
  const [showAudit, setShowAudit] = useState(false);
  // 退款弹窗
  const [showRefund, setShowRefund] = useState(false);
  // WebSocket测试弹窗
  const [showWebSocketTest, setShowWebSocketTest] = useState(false);
  const { hosList, fetchHosList } = useModel('hospital');

  // WebSocket和音频通知相关状态
  const wsRef = useRef<any>(null);
  const audioNotificationRef = useRef<any>(null);

  // 显示通知并播放声音
  const showNotification = (title: string, content: string, type: 'success' | 'info' | 'warning' | 'error' = 'info') => {
    notification[type]({
      message: title,
      description: content,
      placement: 'bottomRight',
      duration: 4.5,
    });

    // 播放提醒声音
    if (audioNotificationRef.current) {
      audioNotificationRef.current.play().catch((error: any) => {
        console.warn('播放提醒声音失败:', error);
      });
    }
  };

  // 处理WebSocket消息
  const handleWebSocketMessage = (data: any) => {
    console.log('收到WebSocket消息:', data);

    // 根据消息类型显示不同的通知
    switch (data.type) {
      case 'new_prescription':
        showNotification(
          '新处方通知',
          `患者 ${data.patientName} 的新处方已提交，处方编号：${data.prescriptionNo}`,
          'info'
        );
        // 刷新表格数据
        actionRef.current?.reload();
        break;
      case 'prescription_audit':
        showNotification(
          '处方审核通知',
          `处方 ${data.prescriptionNo} 审核状态已更新：${data.status}`,
          data.status === '审核通过' ? 'success' : 'warning'
        );
        actionRef.current?.reload();
        break;
      case 'prescription_refund':
        showNotification(
          '处方退费通知',
          `处方 ${data.prescriptionNo} 已申请退费，金额：¥${data.amount}`,
          'warning'
        );
        actionRef.current?.reload();
        break;
      case 'prescription_urgent':
        showNotification(
          '紧急处方通知',
          `紧急处方需要处理：${data.prescriptionNo}，患者：${data.patientName}`,
          'error'
        );
        actionRef.current?.reload();
        break;
      case 'system_message':
        showNotification(
          '系统通知',
          data.message,
          'info'
        );
        break;
      default:
        showNotification(
          '消息通知',
          data.message || '收到新消息',
          'info'
        );
    }
  };

  // 初始化WebSocket连接
  const initWebSocket = async () => {
    try {
      // 动态导入WebSocket工具类
      const { PrescriptionWebSocket, getWebSocketUrl } = await import('@/utils/websocket');

      const wsConfig = {
        url: getWebSocketUrl(),
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        heartbeatInterval: 30000,
      };

      wsRef.current = new PrescriptionWebSocket(wsConfig);

      // 设置消息处理器
      wsRef.current.onMessage(handleWebSocketMessage);

      // 连接WebSocket
      await wsRef.current.connect();

      // 发送用户身份验证信息
      if (initialState?.currentUser) {
        wsRef.current.send({
          type: 'auth',
          userId: initialState.currentUser.id,
          userType: 'prescription_manager'
        });
      }
    } catch (error) {
      console.error('初始化WebSocket失败:', error);
    }
  };

  useEffect(() => {
    fetchHosList();

    // 初始化音频通知
    const initAudioNotification = async () => {
      try {
        const { createNotificationAudio } = await import('@/utils/audioNotification');
        audioNotificationRef.current = createNotificationAudio();
      } catch (error) {
        console.error('初始化音频通知失败:', error);
      }
    };

    // 初始化音频和WebSocket
    initAudioNotification();
    initWebSocket();

    // 组件卸载时清理资源
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      if (audioNotificationRef.current) {
        audioNotificationRef.current.destroy();
        audioNotificationRef.current = null;
      }
    };
  }, []);
  

  const columns: ProColumns<PRESCRIPTION.ItemListItem>[]  = [
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => {
        return record.hospitalName;
      },
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.filter(c => c.value?.length === 12 &&  c.value[0] === 'P').map((c: ListType) => {
          return {
            value: c.value,
            label: c.label,
          };
        })
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear/>
        );
      },
      hideInSearch: !initialState.currentUser.saID  ? false : true,
      hideInTable: true,
      hideInDescriptions: true,
    },
    {
      title: '药店名称',
      dataIndex: 'institutionName',
      hideInTable: !initialState.currentUser.saID ? false : true,
      hideInSearch: true,
    },
    {
      title: '患者ID',
      dataIndex: 'patientID',
      hideInSearch: true,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
    },
    {
      title: '患者性别',
      dataIndex: 'sex',
      render: (_, record) => {
        return formatIdCard(record.patientInfo.idCard, 2)
      },
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '处方编号',
      dataIndex: 'circulationNumber',
    },
    {
      title: '处方类型',
      dataIndex: 'isMedical',
      valueEnum: {
        '0': {
          text: '自费',
        },
        '1' : {
          text: '医保',
        },
      }
    },
    {
      title: '初步诊断',
      dataIndex: 'diagnosis',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '处方信息',
      dataIndex: 'prescriptionInfoJson',
      render: (prescription: any) => 
      {
        return prescription.map(item=>{
          return <>
            <div key={item}>{ item.ypmc }*{item.sl}</div>
          </>
        })
      },
      hideInSearch: true,
      hideInDescriptions: true,
    },
    {
      title: '处方信息',
      dataIndex: 'prescriptionInfoJson',
      render: (prescription: any) => 
        {
          return <table>
            <th>名称 * 数量</th>
            <th>厂家</th>
            <th>规格</th>
            <th>用法用量</th>
            {
              prescription.map(item=>{
                return <tr key={item}>
                  <td style={{textAlign: 'center'}}>{item.ypmc} * {item.sl}{item.dw || ""}</td>
                  <td style={{textAlign: 'center'}}>{item.sccj}</td>
                  <td style={{textAlign: 'center'}}>{item.gg}</td>
                  <td style={{textAlign: 'center'}}>{item.yf}&nbsp;{item.yfyl}</td>
                </tr>
              })
            }
          </table>
        },
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '开方时间',
      dataIndex: 'createTime',
      hideInSearch: true,
    },
    {
      title: '开方时间',
      dataIndex: 'createTime',
      hideInTable: true,
      hideInDescriptions: true,
      valueType: 'dateRange', 
      search: {
        transform: (value) => {
          return {
            beginTime: value[0],
            endTime: value[1],
          };
        },
      },
      initialValue: initialState.currentUser.saID ? [dayjs(), dayjs()] : null
    },
    {
      title: '医保处方备案编号',
      dataIndex: 'recordNumber',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '处方状态',
      dataIndex: 'state',
      // valueEnum: {
      //   '0': '未知',
      //   '1': '正常',
      //   '2': '已退',
      // },
      render: (_, record) => {
        if(record.isRevoke === "1" && record.state !== "2"){
          return '处方已作废'
        }else{
          if(record.state === "0"){
            return  '状态未知'
          }else if(record.state === "1"){
            return  '处方正常'
          } else if (record.state === "2"){
            return  '处方已退费'
          } else if (record.state === "3"){
            return  '处方已作废'
          }else{
            return  ''
          }
        }
      },
      // hideInTable: true,
      hideInSearch: true,
    },
    // {
    //   title: '审核状态',
    //   dataIndex: 'checkState',
    //   valueEnum: {
    //     '0': {
    //       text: '未审核',
    //       status: 'Warning'
    //     },
    //     '1' : {
    //       text: '已审核',
    //       status: 'Success'
    //     },
    //     '2' : {
    //       text: '审核不通过',
    //       status: 'default'
    //     }
    //   }
    // },
    {
      title: '使用状态',
      dataIndex: 'dispensingState',
      valueEnum: {
        '0': '未使用',
        '1': '已使用',
      },
    },
    {
      title: '打印状态',
      dataIndex: 'printState',
      valueEnum: {
        '0': '未打印',
        '1': '已打印',
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      hideInDescriptions: true,
      render: (_, record) => {
        return record.isRevoke === '1' && record.state !== '2' ?
        [ <a
          key="detail"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <div key="refund">处方已作废</div>,
        record.state !== '2' && !initialState.currentUser.saID && <a
          key="detail"
          onClick={async () => {
              Modal.confirm({
                title: `确认这张处方需要退费？`,
                icon: <ExclamationCircleOutlined />,
                content: <div style={{marginTop: 20}}>
                  <p>药店名称：{record.institutionName}</p>
                  <p>顾客姓名：{record.patientName}</p>
                  <p>处方明细：</p>
                  <table>
                    <th>名称 * 数量</th>
                    <th>厂家</th>
                    {
                      record.prescriptionInfoJson.map(item=>{
                        return <tr key={item}>
                          <td style={{textAlign: 'center'}}>{item.ypmc} * {item.sl}{item.dw || ""}</td>
                          <td style={{textAlign: 'center'}}>{item.sccj}</td>
                        </tr>
                      })
                    }
                  </table>
                  { record.fatherInvoice && <p style={{color: 'red', marginTop: 10}}>注意：该处方非当日首次问诊处方，退费将退当日首次问诊处方。</p> }
                </div>,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  setCurrentRow(record);
                  setShowRefund(true)
                },
              });
          }}>
            退费
        </a>
      ]
        :
        record.state === '2' ? 
        [ <a
          key="detail"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        <div key="refund">处方已退费</div>] : [
        <a
          key="detail"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            setShowDetail(true);
          }}
        >
          详情
        </a>,
        record.checkState === '0' && record.prescriptionDoctor &&  <a 
          key="media"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            setShowAudit(true)
          }}
        >
          药师签名
        </a>,
        !record.prescriptionDoctor && <div key="wait">等待开方</div>,
        record.checkState=== '1' && record.dispensingState=== '0' && <a 
          key="media"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            await handleUpdate({ dispensingState: '1', id: record.id});
            setShowPreview(true)
          }}
        >
          使用处方
        </a>,
        record.checkState=== '1' && record.dispensingState=== '1' && <a 
          key="media"
          onClick={async () => {
            const data = await handlePrescriptionInfo({id: record.id})
            setCurrentRow(data);
            setShowPreview(true)
          }}
        >
          查看处方
        </a>,
        !initialState.currentUser.saID && <a
          key="detail"
          onClick={async () => {
              Modal.confirm({
                title: `确认这张处方需要退费？`,
                icon: <ExclamationCircleOutlined />,
                content: <div style={{marginTop: 20}}>
                  <p>药店名称：{record.institutionName}</p>
                  <p>顾客姓名：{record.patientName}</p>
                  <p>处方明细：</p>
                  <table>
                    <thead>
                      <th>名称 * 数量</th>
                      <th>厂家</th>
                    </thead>
                    <tbody>
                      {
                        record.prescriptionInfoJson.map(item=>{
                          return <tr key={item}>
                            <td style={{textAlign: 'center'}}>{item.ypmc} * {item.sl}{item.dw || ""}</td>
                            <td style={{textAlign: 'center'}}>{item.sccj}</td>
                          </tr>
                        })
                      }
                    </tbody>
                  </table>
                  { record.fatherInvoice && <p style={{color: 'red', marginTop: 10}}>注意：该处方非当日首次问诊处方，退费将退当日首次问诊处方。</p> }
                </div>,
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                  setCurrentRow(record);
                  setShowRefund(true)
                },
              });
          }}>
            退费
        </a>
      ]}}
  ];
  return (
    <>
    <PageContainer header={{breadcrumb:{}}}>
      <ProTable
        headerTitle={'查询表格'}
        actionRef={actionRef}
        rowKey="id"
        scroll={{ x: 1500 }}
        rowClassName={(record, idx) => {
          if(record.isRevoke === '1'){
            return styles.greyRow;
          }else{
            if (record.state === '1' && record.checkState === '0')
              return styles.yellowRow;
            else if (record.state === '1' && record.checkState === '1' && record.printState === '0')
              return styles.blueRow;
            else if (record.state === '1' && record.checkState === '1' && record.printState === '1') 
              return styles.greyRow;
            else if (record.state === '2')
              return styles.greyRow;
            else
              return ''
          }
        }}
        search={{
          labelWidth: 120,
          defaultCollapsed: true,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          // 只在开发环境显示WebSocket测试按钮
          process.env.NODE_ENV === 'development' && (
            <Button
              key="websocket-test"
              type="dashed"
              onClick={() => setShowWebSocketTest(true)}
            >
              WebSocket测试
            </Button>
          ),
        ].filter(Boolean)}
        params={initialState.currentUser.saID ? {
          saID: initialState.currentUser.saID
        }: {}}
        request={queryPrescriptionList}
        columns={columns}
      />
      { showDetail && currentRow && <Drawer
        title="处方详情"
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
        extra={
          <Space>
            { currentRow.state === '1' && currentRow.checkState === '0' && currentRow.prescriptionDoctor && <Button type="primary" onClick={async () => {
            setCurrentRow(currentRow);
            setShowAudit(true)
          }}>药师签名</Button>} 
          </Space>
        }
      >
        {currentRow?.id && (
          <ProDescriptions
            column={1}
            title={currentRow?.patientName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<PRESCRIPTION.ItemListItem>[]}
          />
        )}
      </Drawer>
      }
      { 
        showPreview && <Preview 
        values={currentRow} 
        onClosed={()=>{ 
          setShowPreview(false);
          actionRef.current?.reload?.();
        }} 
        onHandlePrint= { async () =>{
          if(currentRow?.isRevoke === "1"){
            message.error('处方已作废，无法继续操作')
            return false
          }else{
            if(currentRow?.state === "2"){
              message.error('处方已退费，无法继续操作')
              return false
            }
          }
          if(currentRow?.isMedical === "1" && !currentRow?.recordNumber){
            message.error('处方未完成医保上传，处方无医保备案号，请等待处方医保上传完成')
            return false
          }
          await handleUpdate({ printState: '1', id: currentRow?.id});
          return true
        }}
        onHandleRefresh = { async () =>{
          const data = await handlePrescriptionInfo({id: currentRow.id})
          setCurrentRow(data);
        }}
        />
      }
      {showAudit && (
        <AuditForm
          values={currentRow || {}}
          onSubmit={async (value) => {
            const success = await handleUpdate({ ...value, id: currentRow?.id});

            if (success) {
              setShowAudit(false);
              setCurrentRow(undefined);

              if (actionRef.current) {
                actionRef.current?.reload?.();
              }
            }
          }}
          onCancel={() => {
            setShowAudit(false);
          }}
          modalVisible={showAudit}
        />
      )}
      {
        showRefund && (
          <RefundForm
            values={currentRow || {}}
            onSubmit={async (value) => {
              const success = await handleRefund({ ...value});

              if (success) {
                setShowRefund(false);
                setCurrentRow(undefined);

                if (actionRef.current) {
                  actionRef.current?.reload?.();
                }
              }
            }}
            onCancel={() => {
              setShowRefund(false);
            }}
            modalVisible={showRefund}
          />
        )
      }

      {/* WebSocket测试弹窗 - 仅开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <Modal
          title="WebSocket通知测试"
          open={showWebSocketTest}
          onCancel={() => setShowWebSocketTest(false)}
          footer={null}
          width={800}
          destroyOnClose
        >
          <WebSocketTest />
        </Modal>
      )}
    </PageContainer>
    </>
  );
};

export default TableList;
