declare namespace PRESCRIPTION {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 开单项目
    type ItemListItem = {        
        isTest?: boolean; // 是否测试数据
        saID?: string;      // 机构ID
        institutionName?: string; // 机构名称
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        patientName?: string; // 患者姓名
        invoice?: string;  // 发票号
        fatherInvoice?: string;  // 父发票号
        describes?: string; // 病情描述
        diagnosis?: string;  // 诊断信息
        prescriptionStr?: string;     // 处方信息
        prescription?: any;    // 处方信息
        prescriptionInfoJson?: any; // 处方json数据
        signData?: string;    // 签名数据
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number | string;    // 状态
        hospitalName?: string; //机构名称,
        doubleCheckDoctor?: string;
        checkPharmacist?: string;
        dispensingDoctor?: string;
        checkState?: string;
        printState?: string;
        dispensingState?: string;
        patientInfo?: any;
        prescriptionNo?: string;
        prescriptionDoctor?: string; 
        isRevoke?: string; // 撤销
        isMedical?: string;  // 是否医保
        recordNumber?: string; // 备案号
    };
    // 项目列表
    type List = {
        /** 列表的内容 **/
        data?: ItemListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}