import React, { useState, createContext } from 'react';
import { useModel, history } from '@umijs/max';
import { RollbackOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { But<PERSON>, Card, message, Space } from 'antd';
import { Qn<PERSON><PERSON>le, QnRemark, QnActions, QnSubmitButton } from './CardItem';
import QnQuestions from './QnQuestions';
import '../style.less';

export const QuestionFuncContext = createContext<any>(null);

export interface CardIndexProps {
  questions: QUESTION.QuestionListItem[];
  onAddQuestion?: (question: QUESTION.QuestionListItem) => void;
  onUpdateQuestion?: (index: number, question: QUESTION.QuestionListItem) => void;
  onDeleteQuestion?: (index: number) => void;
  readOnly?: boolean;
  detailType?: string;
  handleSubmitQuestionnaire?: () => void;
  handleSaveQuestionnaire?: () => void;
}

const CardIndex: React.FC<CardIndexProps> = ({
  questions: propQuestions = [],
  onAddQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  readOnly = false,
  detailType,
  handleSubmitQuestionnaire,
  handleSaveQuestionnaire,
}) => {
  const { initialState } = useModel('@@initialState');
  const [questions, setQuestions] = useState<QUESTION.QuestionListItem[]>(propQuestions);
  const [addAreaVisible, setAddAreaVisible] = useState(false);


  // 更新问题列表
  const handleUpdateFields = (data: QUESTION.QuestionListItem[]) => {
    setQuestions(data);
    onUpdateQuestion?.(0, data[0]); // 通知父组件更新
  };

  // 添加新问题
  const handleUpdateQuestionsLists = (data: QUESTION.QuestionListItem[]) => {
    const tempArr = [...questions];
    const newArr = tempArr.concat(data);
    setAddAreaVisible(false);
    handleUpdateFields(newArr);
    
    // 通知父组件
    data.forEach(question => {
      onAddQuestion?.(question);
    });
  };
  


  // 隐藏添加问题区域
  const handleCancelAdd = () => {
    setAddAreaVisible(false);
  };

  // 删除问题
  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
    onDeleteQuestion?.(index);
  };

  // 更新单个问题
  const handleUpdateSingleQuestion = (index: number, question: QUESTION.QuestionListItem) => {
    const newQuestions = [...questions];
    newQuestions[index] = question;
    setQuestions(newQuestions);
    onUpdateQuestion?.(index, question);
  };

  // 移动问题
  const handleShiftQuestion = (index: number, direction: number) => {
    const newIndex = index + direction;
    if (newIndex < 0 || newIndex >= questions.length) return;

    const newQuestions = [...questions];
    const [movedQuestion] = newQuestions.splice(index, 1);
    newQuestions.splice(newIndex, 0, movedQuestion);

    setQuestions(newQuestions);
    // 通知父组件更新整个问题列表
    newQuestions.forEach((question, idx) => {
      onUpdateQuestion?.(idx, question);
    });
  };

  // 复制问题
  const handleCopyQuestion = (index: number) => {
    const question = questions[index];
    const newQuestion = {
      ...question,
      id: Date.now(),
      title: `${question.title}(副本)`,
      options: question.options ? [...question.options] : undefined
    };

    const newQuestions = [...questions];
    newQuestions.splice(index + 1, 0, newQuestion);

    setQuestions(newQuestions);
    onAddQuestion?.(newQuestion);
  };


  // 保存问卷
  const handleSave = () => {
    if (questions.length === 0) {
      message.error('请至少添加一个问题');
      return;
    }
    handleSaveQuestionnaire?.();
  };

  // 提交问卷
  const handleSubmit = () => {
    if (questions.length === 0) {
      message.error('请至少添加一个问题');
      return;
    }
    handleSubmitQuestionnaire?.();
  };

	const handleAddRadio=()=>{
		const newQuestion = {
			// type: 'radio',
			type: 1,
			title: '问题标题',
			options: ['选项1','选项2'],
			// data: []
		};
    handleUpdateQuestionsLists([newQuestion])
	}

	const handleAddCheckbox=()=>{
		const newQuestion = {
			// type: 'checkbox',
			type: 2,  
			title: '问题标题',
			options: ['选项1','选项2'],
		};
    handleUpdateQuestionsLists([newQuestion])
	}

	const handleAddTextArea=()=>{
		const newQuestion = {
			type: 3,
			title: '问题标题',
			required: false
		};
    handleUpdateQuestionsLists([newQuestion])
	}

  const contextValue = {
    questions,
    setQuestions,
    addAreaVisible,
    setAddAreaVisible,
    handleUpdateFields,
    handleUpdateQuestionsLists,
    handleCancelAdd,
    handleDeleteQuestion: handleDeleteQuestion,
    handleUpdateSingleQuestion,
    readOnly,
  };

  return (
    <QuestionFuncContext.Provider value={contextValue}>
      <div className="question-detail-container">
        {/* 问卷标题和描述 */}
        <Card className="question-header-card">
          <QnTitle readOnly={readOnly} />
          <QnRemark readOnly={readOnly} />
        </Card>

        {/* 问题列表 */}
        <Card 
          className="question-content-card"
          title="问题列表"
          extra={
            !readOnly && (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={handleAddTextArea}
                disabled={addAreaVisible}
              >
                添加问题
              </Button>
            )
          }
        >
          <QnQuestions
            questions={questions}
            onUpdateQuestion={handleUpdateSingleQuestion}
            onDeleteQuestion={handleDeleteQuestion}
            onShiftQuestion={handleShiftQuestion}
            onCopyQuestion={handleCopyQuestion}
            readOnly={readOnly}
          />
        </Card>
        {/* 操作组件 */}
        <QnActions
          readOnly={readOnly}
          handleAddRadio={handleAddRadio}
          handleAddCheckbox={handleAddCheckbox}
          handleAddTextArea={handleAddTextArea}                
        />
        {/* 提交按钮组件 */}
        <QnSubmitButton 
          onSave={handleSave}
          onSubmit={handleSubmit}
          readOnly={readOnly}
        />
      </div>
    </QuestionFuncContext.Provider>
  );
};

export default CardIndex;
