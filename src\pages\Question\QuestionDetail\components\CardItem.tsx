import React, { useState } from 'react';
import { 
  CheckCircleOutlined, 
  CheckSquareOutlined, 
  FileTextOutlined, 
  PlusOutlined, 
  CloseOutlined, 
  EditOutlined, 
  PlusCircleOutlined, 
  MinusCircleOutlined,
  SaveOutlined,
  RollbackOutlined
} from '@ant-design/icons';
import { Button, Input, Space, Form } from 'antd';

const { TextArea } = Input;

export interface QnTitleProps {
  questionnaireValues?: any;
  setQuestionsnanire?: (key: string, value: any) => void;
  readOnly?: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

export const QnTitle: React.FC<QnTitleProps> = ({
  questionnaireValues,
  setQuestionsnanire,
  readOnly = false,
  value,
  onChange,
}) => {
  const [isEditable, setIsEditable] = useState(false);
  const [title, setTitle] = useState(
    value || questionnaireValues?.title || '此处添加问卷标题'
  );

  const handleTitleClick = () => {
    if (!readOnly) {
      setIsEditable(true);
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setTitle(newTitle);
    setQuestionsnanire?.('title', newTitle);
    onChange?.(newTitle);
  };

  const handleTitleBlur = () => {
    setIsEditable(false);
  };

  if (readOnly) {
    return (
      <div className="editTitle" style={{ padding: 20, textAlign: 'center' }}>
        <h2 style={{ fontSize: 18, fontWeight: 'bold', margin: 0 }}>
          {title}
        </h2>
      </div>
    );
  }

  return isEditable ? (
    <div 
      className="editTitle" 
      style={{ padding: 3, textAlign: 'center' }} 
    >
      <Input 
        style={{ 
          fontSize: 18, 
          fontWeight: 'bold', 
          padding: 30, 
          textAlign: 'center' 
        }} 
        value={title} 
        onChange={handleTitleChange} 
        onBlur={handleTitleBlur}
        onPressEnter={handleTitleBlur}
        autoFocus
      />
    </div>
  ) : (
    <div 
      className="editTitle" 
      style={{ 
        padding: 20, 
        textAlign: 'center',
        cursor: 'pointer',
        border: '1px dashed #d9d9d9',
        borderRadius: 4,
      }} 
      onClick={handleTitleClick}
    >
      <h2 style={{ 
        fontSize: 18, 
        fontWeight: 'bold', 
        margin: 0,
        color: title === '此处添加问卷标题' ? '#999' : '#333'
      }}>
        {title}
      </h2>
      <EditOutlined style={{ marginLeft: 8, color: '#999' }} />
    </div>
  );
};

export interface QnRemarkProps {
  questionnaireValues?: any;
  setQuestionsnanire?: (key: string, value: any) => void;
  readOnly?: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

export const QnRemark: React.FC<QnRemarkProps> = ({
  questionnaireValues,
  setQuestionsnanire,
  readOnly = false,
  value,
  onChange,
}) => {
  const [isEditable, setIsEditable] = useState(false);
  const [remark, setRemark] = useState(
    value || questionnaireValues?.description || '此处添加问卷描述'
  );

  const handleRemarkClick = () => {
    if (!readOnly) {
      setIsEditable(true);
    }
  };

  const handleRemarkChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newRemark = e.target.value;
    setRemark(newRemark);
    setQuestionsnanire?.('description', newRemark);
    onChange?.(newRemark);
  };

  const handleRemarkBlur = () => {
    setIsEditable(false);
  };

  if (readOnly) {
    return (
      <div className="editRemark" style={{ padding: 20, textAlign: 'center' }}>
        <p style={{ margin: 0, color: '#666' }}>
          {remark}
        </p>
      </div>
    );
  }

  return isEditable ? (
    <div 
      className="editRemark" 
      style={{ padding: 10, textAlign: 'center' }} 
    >
      <TextArea 
        style={{ textAlign: 'center' }} 
        value={remark} 
        onChange={handleRemarkChange} 
        onBlur={handleRemarkBlur}
        autoSize={{ minRows: 2, maxRows: 4 }}
        autoFocus
      />
    </div>
  ) : (
    <div 
      className="editRemark" 
      style={{ 
        padding: 20, 
        textAlign: 'center',
        cursor: 'pointer',
        border: '1px dashed #d9d9d9',
        borderRadius: 4,
        marginTop: 16,
      }} 
      onClick={handleRemarkClick}
    >
      <p style={{ 
        margin: 0, 
        color: remark === '此处添加问卷描述' ? '#999' : '#666'
      }}>
        {remark}
      </p>
      <EditOutlined style={{ marginLeft: 8, color: '#999' }} />
    </div>
  );
};

export interface QnActionsProps {
  readOnly?: boolean;
  handleAddInput?: () => void;
  handleAddRadio?: () => void;
  handleAddCheckbox?: () => void;
  handleAddTextArea?: () => void;
}

export const QnActions: React.FC<QnActionsProps> = ({
    readOnly,
    handleAddRadio,
    handleAddCheckbox,
    handleAddTextArea
}) => {
    if (readOnly) {
      return null;
    }
    return (
      <div style={{ padding: 30, textAlign: 'center', border: '1px solid #eee' }}>
        <Button icon={<CheckCircleOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddRadio}>单选</Button>
        <Button icon={<CheckSquareOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddCheckbox}>多选</Button>
        <Button icon={<FileTextOutlined />} size="large" style={{ marginLeft: 16 }} onClick={handleAddTextArea}>文本</Button>
      </div>
    )
};

export interface QnSubmitButtonProps {
  onSave?: () => void;
  onSubmit?: () => void;
  readOnly?: boolean;
  loading?: boolean;
}

export const QnSubmitButton: React.FC<QnSubmitButtonProps> = ({
  onSave,
  onSubmit,
  readOnly = false,
  loading = false,
}) => {
  if (readOnly) {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: 24, 
      right: 24, 
      zIndex: 1000 
    }}>
      <Space direction="vertical">
        <Button 
          type="default" 
          icon={<SaveOutlined />}
          onClick={onSave}
          loading={loading}
          size="large"
        >
          保存
        </Button>
        <Button 
          type="primary" 
          onClick={onSubmit}
          icon={ <PlusCircleOutlined />}
          loading={loading}
          size="large"
        >
          发布
        </Button>
      </Space>
    </div>
  );
};

// 问题类型图标映射
export const getQuestionTypeIcon = (type: number) => {
  switch (type) {
    case 1:
      return <CheckCircleOutlined />;
    case 2:
      return <CheckSquareOutlined />;
    case 3:
      return <FileTextOutlined />;
    default:
      return <FileTextOutlined />;
  }
};

// 问题类型名称映射
export const getQuestionTypeName = (type: number) => {
  switch (type) {
    case 1:
      return '单选题';
    case 2:
      return '多选题';
    case 3:
      return '文本题';
    default:
      return '未知类型';
  }
};
