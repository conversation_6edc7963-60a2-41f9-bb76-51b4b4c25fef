import React, { useContext, useMemo } from 'react';
import { 
  Input, 
  Radio, 
  Checkbox, 
  Button, 
  Card, 
  Space, 
  Divider,
  Switch,
  Select,
  Form,
} from 'antd';
import { 
  MinusCircleOutlined, 
  PlusOutlined, 
  ArrowUpOutlined, 
  ArrowDownOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
} from '@ant-design/icons';
import { QuestionFuncContext } from './CardIndex';
import { getQuestionTypeIcon, getQuestionTypeName } from './CardItem';

const { TextArea } = Input;
const { Option } = Select;

export interface QnQuestionsProps {
  questions: QUESTION.QuestionListItem[];
  onUpdateQuestion?: (index: number, question: QUESTION.QuestionListItem) => void;
  onDeleteQuestion?: (index: number) => void;
  onShiftQuestion?: (index: number, direction: number) => void;
  onCopyQuestion?: (index: number) => void;
  readOnly?: boolean;
}

interface QuestionButtonsProps {
  questionIndex: number;
  array: QUESTION.QuestionListItem[];
  question: QUESTION.QuestionListItem;
  onShiftQuestion?: (index: number, direction: number) => void;
  onCopyQuestion?: (index: number) => void;
  onRemoveQuestion?: (index: number) => void;
  onChangeQuestion?: (index: number, field: string, value: any) => void
  readOnly?: boolean;
}

const QuestionButtons: React.FC<QuestionButtonsProps> = ({
  questionIndex,
  array,
  question,
  onShiftQuestion,
  onCopyQuestion,
  onRemoveQuestion,
  onChangeQuestion,
  readOnly = false,
}) => {
  if (readOnly) {
    return null;
  }

  return (
    <div style={{ textAlign: 'right', marginTop: 16 }}>
      <Space>
        <span>必填:</span>
        <Switch
          size="small"
          checked={question.required}
          onChange={(checked) => onChangeQuestion(questionIndex, 'required', checked)}
        />
        <Select
          size="small"
          value={question.type || 1}
          onChange={(value) => onChangeQuestion(questionIndex, 'type', value)}
          style={{ width: 100 }}
        >
          <Option value={1}>单选题</Option>
          <Option value={2}>多选题</Option>
          <Option value={3}>文本题</Option>
        </Select>
        {questionIndex > 0 && (
          <Button 
            size="small" 
            icon={<ArrowUpOutlined />}
            onClick={() => onShiftQuestion?.(questionIndex, -1)}
          >
            上移
          </Button>
        )}
        {questionIndex < array.length - 1 && (
          <Button 
            size="small" 
            icon={<ArrowDownOutlined />}
            onClick={() => onShiftQuestion?.(questionIndex, 1)}
          >
            下移
          </Button>
        )}
        <Button 
          size="small" 
          icon={<CopyOutlined />}
          onClick={() => onCopyQuestion?.(questionIndex)}
        >
          复制
        </Button>
        <Button 
          size="small" 
          danger
          icon={<DeleteOutlined />}
          onClick={() => onRemoveQuestion?.(questionIndex)}
        >
          删除
        </Button>
      </Space>
    </div>
  );
};

interface RadioItemProps {
  question: QUESTION.QuestionListItem;
  questionIndex: number;
  array: QUESTION.QuestionListItem[];
  onQuestionChange?: (index: number, field: string, value: any) => void;
  onOptionChange?: (questionIndex: number, optionIndex: number, value: string) => void;
  onAddOption?: (questionIndex: number) => void;
  onRemoveOption?: (questionIndex: number, optionIndex: number) => void;
  readOnly?: boolean;
}

const RadioItem: React.FC<RadioItemProps> = ({
  question,
  questionIndex,
  array,
  onQuestionChange,
  onOptionChange,
  onAddOption,
  onRemoveOption,
  readOnly = false,
}) => {
  const options = question.options || ['选项1', '选项2'];

  return (
    <div className="question-item radio-item">
      <div className="question-header">
        <Space>
          {getQuestionTypeIcon(1)}
          <span className="question-type">{getQuestionTypeName(1)}</span>
        </Space>
      </div>
      
      <div className="question-content">
        {readOnly ? (
          <h4>{question.title}</h4>
        ) : (
          <Input
            placeholder="请输入问题标题"
            value={question.title}
            onChange={(e) => onQuestionChange?.(questionIndex, 'title', e.target.value)}
            style={{ marginBottom: 16, fontWeight: 'bold' }}
          />
        )}
        
        <Radio.Group disabled style={{ width: '100%' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {options.map((option, optionIndex) => (
              <div key={optionIndex} style={{ display: 'flex', alignItems: 'center' }}>
                <Radio value={optionIndex}></Radio>
                {readOnly ? (
                  <span style={{ marginLeft: 8 }}>{option}</span>
                ) : (
                  <Input
                    placeholder={`选项${optionIndex + 1}`}
                    value={option}
                    onChange={(e) => onOptionChange?.(questionIndex, optionIndex, e.target.value)}
                    style={{ marginLeft: 8, flex: 1 }}
                    suffix={
                      options.length > 2 && (
                        <MinusCircleOutlined 
                          onClick={() => onRemoveOption?.(questionIndex, optionIndex)}
                          style={{ color: '#ff4d4f', cursor: 'pointer' }}
                        />
                      )
                    }
                  />
                )}
              </div>
            ))}
          </Space>
        </Radio.Group>
        
        {!readOnly && (
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={() => onAddOption?.(questionIndex)}
            style={{ marginTop: 16, width: '100%' }}
          >
            添加选项
          </Button>
        )}
      </div>
    </div>
  );
};

interface CheckboxItemProps {
  question: QUESTION.QuestionListItem;
  questionIndex: number;
  array: QUESTION.QuestionListItem[];
  onQuestionChange?: (index: number, field: string, value: any) => void;
  onOptionChange?: (questionIndex: number, optionIndex: number, value: string) => void;
  onAddOption?: (questionIndex: number) => void;
  onRemoveOption?: (questionIndex: number, optionIndex: number) => void;
  readOnly?: boolean;
}

const CheckboxItem: React.FC<CheckboxItemProps> = ({
  question,
  questionIndex,
  array,
  onQuestionChange,
  onOptionChange,
  onAddOption,
  onRemoveOption,
  readOnly = false,
}) => {
  const options = question.options || ['选项1', '选项2'];

  return (
    <div className="question-item checkbox-item">
      <div className="question-header">
        <Space>
          {getQuestionTypeIcon(2)}
          <span className="question-type">{getQuestionTypeName(2)}</span>
        </Space>
      </div>
      
      <div className="question-content">
        {readOnly ? (
          <h4>{question.title}</h4>
        ) : (
          <Input
            placeholder="请输入问题标题"
            value={question.title}
            onChange={(e) => onQuestionChange?.(questionIndex, 'title', e.target.value)}
            style={{ marginBottom: 16, fontWeight: 'bold' }}
          />
        )}
        
        <Checkbox.Group disabled style={{ width: '100%' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {options.map((option, optionIndex) => (
              <div key={optionIndex} style={{ display: 'flex', alignItems: 'center' }}>
                <Checkbox value={optionIndex}></Checkbox>
                {readOnly ? (
                  <span style={{ marginLeft: 8 }}>{option}</span>
                ) : (
                  <Input
                    placeholder={`选项${optionIndex + 1}`}
                    value={option}
                    onChange={(e) => onOptionChange?.(questionIndex, optionIndex, e.target.value)}
                    style={{ marginLeft: 8, flex: 1 }}
                    suffix={
                      options.length > 2 && (
                        <MinusCircleOutlined 
                          onClick={() => onRemoveOption?.(questionIndex, optionIndex)}
                          style={{ color: '#ff4d4f', cursor: 'pointer' }}
                        />
                      )
                    }
                  />
                )}
              </div>
            ))}
          </Space>
        </Checkbox.Group>
        
        {!readOnly && (
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={() => onAddOption?.(questionIndex)}
            style={{ marginTop: 16, width: '100%' }}
          >
            添加选项
          </Button>
        )}
      </div>
    </div>
  );
};

interface TextItemProps {
  question: QUESTION.QuestionListItem;
  questionIndex: number;
  onQuestionChange?: (index: number, field: string, value: any) => void;
  readOnly?: boolean;
}

const TextItem: React.FC<TextItemProps> = ({
  question,
  questionIndex,
  onQuestionChange,
  readOnly = false,
}) => {
  return (
    <div className="question-item text-item">
      <div className="question-header">
        <Space>
          {getQuestionTypeIcon(3)}
          <span className="question-type">{getQuestionTypeName(3)}</span>
        </Space>
      </div>
      
      <div className="question-content">
        {readOnly ? (
          <h4>{question.title}</h4>
        ) : (
          <Input
            placeholder="请输入问题标题"
            value={question.title}
            onChange={(e) => onQuestionChange?.(questionIndex, 'title', e.target.value)}
            style={{ marginBottom: 16, fontWeight: 'bold' }}
          />
        )}
        
        <TextArea
          placeholder="用户将在此处填写答案..."
          disabled
          rows={3}
          style={{ backgroundColor: '#f5f5f5' }}
        />
      </div>
    </div>
  );
};

const QnQuestions: React.FC<QnQuestionsProps> = ({
  questions,
  onUpdateQuestion,
  onDeleteQuestion,
  onShiftQuestion,
  onCopyQuestion,
  readOnly = false,
}) => {
  // 处理问题字段变更
  const handleQuestionChange = (index: number, field: string, value: any) => {
    const newQuestion = { ...questions[index], [field]: value };
    onUpdateQuestion?.(index, newQuestion);
  };

  // 处理选项变更
  const handleOptionChange = (questionIndex: number, optionIndex: number, value: string) => {
    const question = questions[questionIndex];
    const newOptions = [...(question.options || [])];
    newOptions[optionIndex] = value;
    handleQuestionChange(questionIndex, 'options', newOptions);
  };

  // 添加选项
  const handleAddOption = (questionIndex: number) => {
    const question = questions[questionIndex];
    const newOptions = [...(question.options || []), `选项${(question.options?.length || 0) + 1}`];
    handleQuestionChange(questionIndex, 'options', newOptions);
  }; 

  // 删除选项
  const handleRemoveOption = (questionIndex: number, optionIndex: number) => {
    const question = questions[questionIndex];
    const newOptions = (question.options || []).filter((_, index) => index !== optionIndex);
    handleQuestionChange(questionIndex, 'options', newOptions);
  };

  // 移动问题
  const handleShiftQuestion = (index: number, direction: number) => {
    const newIndex = index + direction;
    if (newIndex < 0 || newIndex >= questions.length) return;

    if (onShiftQuestion) {
      onShiftQuestion(index, direction);
    } else {
      // 如果父组件没有提供处理函数，则在本地处理
      const newQuestions = [...questions];
      const [movedQuestion] = newQuestions.splice(index, 1);
      newQuestions.splice(newIndex, 0, movedQuestion);

      // 更新所有受影响的问题
      newQuestions.forEach((question, idx) => {
        onUpdateQuestion?.(idx, question);
      });
    }
  };

  // 复制问题
  const handleCopyQuestion = (index: number) => {
    const question = questions[index];
    const newQuestion = {
      ...question,
      id: Date.now(),
      title: `${question.title}(副本)`,
      options: question.options ? [...question.options] : undefined
    };

    if (onCopyQuestion) {
      onCopyQuestion(index);
    } else {
      // 如果父组件没有提供处理函数，则在本地处理
      // 在当前问题后面插入复制的问题
      const insertIndex = index + 1;
      onUpdateQuestion?.(insertIndex, newQuestion);
    }
  };

  // 删除问题
  const handleRemoveQuestion = (index: number) => {
    onDeleteQuestion?.(index);
  };

  const renderQuestion = (question: QUESTION.QuestionListItem, index: number) => {
    const commonProps = {
      question,
      questionIndex: index,
      array: questions,
      onQuestionChange: handleQuestionChange,
      readOnly,
    };

    let questionComponent;
    switch (question.type) {
      case 1:
        questionComponent = (
          <RadioItem
            {...commonProps}
            onOptionChange={handleOptionChange}
            onAddOption={handleAddOption}
            onRemoveOption={handleRemoveOption}
          />
        );
        break;
      case 2:
        questionComponent = (
          <CheckboxItem
            {...commonProps}
            onOptionChange={handleOptionChange}
            onAddOption={handleAddOption}
            onRemoveOption={handleRemoveOption}
          />
        );
        break;
      case 3:
        questionComponent = <TextItem {...commonProps} />;
        break;
      default:
        questionComponent = <TextItem {...commonProps} />;
    }

    return (
      <Card
        key={question.id || index}
        id={`anchorID${index}`}
        className="question-card"
        style={{ marginBottom: 16 }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>问题 {index + 1}</span>
          </div>
        }
        extra={
          <QuestionButtons
            questionIndex={index}
            array={questions}
            question={question}
            onChangeQuestion={handleQuestionChange}
            onShiftQuestion={handleShiftQuestion}
            onCopyQuestion={handleCopyQuestion}
            onRemoveQuestion={handleRemoveQuestion}
            readOnly={readOnly}
          />
        }
      >
        {questionComponent}
      </Card>
    );
  };

  return (
    <div className="qn-questions">
      {questions.map((question, index) => renderQuestion(question, index))}
      
      {questions.length === 0 && (
        <div style={{ 
          textAlign: 'center', 
          padding: '60px 20px',
          color: '#999',
          border: '1px dashed #d9d9d9',
          borderRadius: 4,
        }}>
          <EditOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <div style={{ fontSize: 16, marginBottom: 8 }}>暂无问题</div>
          <div style={{ fontSize: 12 }}>
            请点击&ldquo;添加问题&rdquo;按钮开始创建问卷
          </div>
        </div>
      )}
    </div>
  );
};

export default QnQuestions;
