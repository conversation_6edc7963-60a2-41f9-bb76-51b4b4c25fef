import React, { useState, useEffect, useCallback } from 'react';
import { Card, message, Row, Col, Button } from 'antd';
import { useModel, history, useSearchParams, useParams, useLocation } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-layout';
import { ArrowLeftOutlined, SaveOutlined, EyeOutlined } from '@ant-design/icons';
import {
  addQuestionnaire,
  editQuestionnaire,
  queryListById
} from '@/services/api/question';
import TipModal from './components/TipModal';
import Catalogue from './components/Catalogue';
import CardIndex from './components/CardIndex';
import './style.less';



const QuestionDetail: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const [questionnaireData, setQuestionnaireData] = useState<QUESTION.QuestionnaireListItem>({});
  const [questions, setQuestions] = useState<QUESTION.QuestionListItem[]>([]);
  const [tipModalVisible, setTipModalVisible] = useState(false);

  // 获取URL参数
  const params = useParams();
  const pathId = params.id;
  const mode = searchParams.get('mode') || 'view'; // add, edit, view, preview

  // 如果路径是 /question/list/new，则为新增模式
  const isNewRoute = pathId === 'new';
  const id = isNewRoute ? null : pathId;

  // 从路由状态中获取问卷基本信息
  const routeQuestionnaire = (location.state as any)?.questionnaire;
  
  // 定义加载问卷详情函数
  const loadQuestionnaireDetail = useCallback(async () => {
    if (!id) return;

    try {
      // queryListById 只返回问题列表，不包含问卷基本信息
      const response = await queryListById({ id: parseInt(id) });
      if (response.code === '0') {
        // 使用路由状态中的问卷基本信息
        if (routeQuestionnaire) {
          setQuestionnaireData(routeQuestionnaire);
        }

        // 处理问题列表数据，转换为前端需要的格式
        const questionsList = (response.data || []).map((item: any) => ({
          id: item.id,
          questionnaireid: item.questionnaireid,
          type: item.type,
          title: item.title,
          required: item.required === 1, // 转换为boolean
          sort: item.sort,
          options: item.questionoptionsList || [],
          state: item.state,
        }));

        setQuestions(questionsList);
      } else {
        message.error(response.msg || '加载问卷详情失败');
      }
    } catch (error) {
      message.error('加载问卷详情失败');
    }
  }, [id, routeQuestionnaire]);

  // 初始化问卷基本信息
  useEffect(() => {
    if (routeQuestionnaire) {
      setQuestionnaireData(routeQuestionnaire);
    }
  }, [routeQuestionnaire]);

  useEffect(() => {
    if (id && (mode === 'edit' || mode === 'view' || mode === 'preview')) {
      loadQuestionnaireDetail();
    }
  }, [id, mode, loadQuestionnaireDetail]);



  /**
   * 添加问卷
   */
  const handleAdd = async (fields: any) => {
    const hide = message.loading('正在添加问卷...');
    try {
      const response = await addQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('添加成功');
        return true;
      }
      message.error(response.msg || '添加失败');
      return false;
    } catch (error) {
      hide();
      message.error('添加失败请重试！');
      return false;
    }
  };

  /**
   * 修改问卷
   */
  const handleEdit = async (fields: any) => {
    const hide = message.loading('正在保存问卷...');
    try {
      const response = await editQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('修改成功');
        return true;
      }
      message.error(response.msg || '修改失败');
      return false;
    } catch (error) {
      hide();
      message.error('修改失败请重试！');
      return false;
    }
  };

  /**
   * 保存问卷
   */
  const handleSave = async () => {
    try {
      // 检查基本信息
      if (!questionnaireData.title) {
        message.error('请输入问卷标题');
        return;
      }
      if (!questionnaireData.description) {
        message.error('请输入问卷描述');
        return;
      }
      if (questions.length === 0) {
        message.error('请至少添加一个问题');
        return;
      }

      const submitData = {
        title: questionnaireData.title,
        description: questionnaireData.description,
        questions: questions,
        creator: initialState?.currentUser?.realName || '',
      };

      let success = false;
      if (mode === 'add' || isNewRoute) {
        success = await handleAdd(submitData);
      } else if (mode === 'edit') {
        success = await handleEdit({ ...submitData, id: parseInt(id!) });
      }

      if (success) {
        history.push('/question/list');
      }
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };

  /**
   * 返回列表
   */
  const handleBack = () => {
    history.push('/question/list');
  };

  /**
   * 预览问卷
   */
  const handlePreview = () => {
    setTipModalVisible(true);
  };

  /**
   * 添加问题
   */
  const handleAddQuestion = (question: QUESTION.QuestionListItem) => {
    setQuestions([...questions, { ...question, id: Date.now() }]);
  };

  /**
   * 更新问题
   */
  const handleUpdateQuestion = (index: number, question: QUESTION.QuestionListItem) => {
    const newQuestions = [...questions];
    newQuestions[index] = question;
    setQuestions(newQuestions);
  };

  /**
   * 删除问题
   */
  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const isReadOnly = mode === 'view' || mode === 'preview';
  const pageTitle = mode === 'add' ? '新增问卷' : mode === 'edit' ? '编辑问卷' : mode === 'preview' ? '预览问卷' : '问卷详情';

  return (
    <PageContainer
      header={{
        title: pageTitle,
        breadcrumb: {},
        extra: [
          <Button key="back" icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回
          </Button>,
          !isReadOnly && (
            <Button key="preview" icon={<EyeOutlined />} onClick={handlePreview}>
              预览
            </Button>
          ),
          !isReadOnly && (
            <Button key="save" type="primary" icon={<SaveOutlined />} onClick={handleSave}>
              保存
            </Button>
          ),
        ].filter(Boolean),
      }}
    >
      <Row gutter={24}>
        <Col span={18}>
          <Card title="问题列表">
            <CardIndex
              questions={questions}
              onAddQuestion={handleAddQuestion}
              onUpdateQuestion={handleUpdateQuestion}
              onDeleteQuestion={handleDeleteQuestion}
              readOnly={isReadOnly}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Catalogue
            questions={questions}
            onQuestionClick={(index) => {
              // 滚动到对应问题
              const element = document.getElementById(`question-${index}`);
              element?.scrollIntoView({ behavior: 'smooth' });
            }}
          />
        </Col>
      </Row>

      {tipModalVisible && (
        <TipModal
          visible={tipModalVisible}
          onCancel={() => setTipModalVisible(false)}
          questionnaire={questionnaireData}
          questions={questions}
        />
      )}
    </PageContainer>
  );
};

export default QuestionDetail;
