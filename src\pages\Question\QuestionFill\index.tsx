import React, { useState, useEffect } from 'react';
import { Input, Button, Radio, Checkbox, Modal, Card, Row, Col, message, Form, Space } from 'antd';
import { useModel, history, useSearchParams } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-layout';
import { 
  CheckCircleOutlined, 
  FileTextOutlined, 
  SendOutlined,
  RollbackOutlined 
} from '@ant-design/icons';
import { 
  queryListById, 
  submitAnswer 
} from '@/services/api/question';

const { TextArea } = Input;

interface QuestionOption {
  id: number;
  text: string;
  value?: string;
}

interface QuestionItem {
  id: number;
  title: string;
  type: number; // 1-单选 2-多选 3-文本
  options?: QuestionOption[];
  required: boolean;
  answer?: any;
}

interface QuestionListFormProps {
  questions: QuestionItem[];
  form: any;
  onValuesChange?: (changedValues: any, allValues: any) => void;
}

const QuestionListForm: React.FC<QuestionListFormProps> = ({
  questions,
  form,
  onValuesChange,
}) => {
  const optionStyle: React.CSSProperties = {
    display: 'block',
    height: '32px',
    lineHeight: '32px',
    marginBottom: 8,
  };

  const renderQuestion = (question: QuestionItem, questionIndex: number) => {
    const fieldName = `question${question.id}`;
    
    let questionComponent;
    
    switch (question.type) {
      case 1: // 单选题
        questionComponent = (
          <Radio.Group style={{ width: '100%' }}>
            {question.options?.map((option, optionIndex) => (
              <Radio 
                style={optionStyle} 
                value={option.id} 
                key={optionIndex}
              >
                {option.text}
              </Radio>
            ))}
          </Radio.Group>
        );
        break;
        
      case 2: // 多选题
        questionComponent = (
          <Checkbox.Group style={{ width: '100%' }}>
            {question.options?.map((option, optionIndex) => (
              <Checkbox 
                style={optionStyle} 
                value={option.id} 
                key={optionIndex}
              >
                {option.text}
              </Checkbox>
            ))}
          </Checkbox.Group>
        );
        break;
        
      case 3: // 文本题
        questionComponent = (
          <TextArea
            placeholder="请输入您的答案..."
            rows={4}
            maxLength={500}
            showCount
          />
        );
        break;
        
      default:
        questionComponent = (
          <TextArea
            placeholder="请输入您的答案..."
            rows={4}
            maxLength={500}
            showCount
          />
        );
    }

    return (
      <Col span={24} key={question.id} style={{ marginBottom: 24 }}>
        <Card 
          className="question-card"
          style={{ 
            border: '1px solid #f0f0f0',
            borderRadius: 8,
          }}
        >
          <Form.Item
            name={fieldName}
            label={
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                <span style={{ 
                  background: '#1890ff', 
                  color: 'white', 
                  padding: '4px 8px', 
                  borderRadius: 4, 
                  marginRight: 12,
                  fontSize: 12,
                }}>
                  Q{questionIndex + 1}
                </span>
                <span style={{ fontSize: 16, fontWeight: 500 }}>
                  {question.title}
                </span>
                {question.required && (
                  <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>
                )}
              </div>
            }
            rules={[{
              required: question.required, 
              message: '此题为必填项，请完成后再提交'
            }]}
            style={{ marginBottom: 0 }}
          >
            {questionComponent}
          </Form.Item>
        </Card>
      </Col>
    );
  };

  return (
    <Form 
      form={form} 
      layout="vertical"
      onValuesChange={onValuesChange}
    >
      <Row gutter={[0, 16]}>
        {questions.map((question, questionIndex) => 
          renderQuestion(question, questionIndex)
        )}
      </Row>
    </Form>
  );
};

const QuestionFill: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [questionnaireData, setQuestionnaireData] = useState<any>({});
  const [questions, setQuestions] = useState<QuestionItem[]>([]);
  const [answers, setAnswers] = useState<Record<string, any>>({});

  // 获取URL参数
  const questionnaireId = searchParams.get('id');
  const deviceCode = searchParams.get('deviceCode');

  useEffect(() => {
    if (questionnaireId) {
      loadQuestionnaireData();
    }
  }, [questionnaireId]);

  /**
   * 加载问卷数据
   */
  const loadQuestionnaireData = async () => {
    if (!questionnaireId) return;
    
    setLoading(true);
    try {
      const response = await queryListById({ questionnaireId });
      if (response.code === '0') {
        setQuestionnaireData(response.data.questionnaire || {});
        setQuestions(response.data.questions || []);
      } else {
        message.error(response.msg || '加载问卷失败');
      }
    } catch (error) {
      message.error('加载问卷失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理表单值变化
   */
  const handleValuesChange = (changedValues: any, allValues: any) => {
    setAnswers(allValues);
  };

  /**
   * 提交答卷
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      setSubmitting(true);
      
      // 构造提交数据
      const submitData = {
        questionnaireId: parseInt(questionnaireId!),
        deviceCode: deviceCode || 'unknown',
        answererName: initialState?.currentUser?.realName || '匿名用户',
        answers: Object.keys(values).map(key => {
          const questionId = key.replace('question', '');
          return {
            questionId: parseInt(questionId),
            answer: Array.isArray(values[key]) ? values[key].join(',') : values[key],
          };
        }),
      };

      const response = await submitAnswer(submitData);
      
      if (response.code === '0') {
        message.success('提交成功，感谢您的参与！');
        // 可以跳转到成功页面或关闭窗口
        setTimeout(() => {
          history.push('/question/list');
        }, 2000);
      } else {
        message.error(response.msg || '提交失败');
      }
    } catch (error) {
      message.error('请完善必填项后再提交');
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * 返回列表
   */
  const handleBack = () => {
    history.push('/question/list');
  };

  return (
    <PageContainer
      header={{
        title: questionnaireData.title || '问卷填写',
        breadcrumb: {},
        extra: [
          <Button key="back" icon={<RollbackOutlined />} onClick={handleBack}>
            返回
          </Button>,
        ],
      }}
      loading={loading}
    >
      <Card>
        {/* 问卷描述 */}
        {questionnaireData.description && (
          <div style={{ 
            padding: '24px 0',
            borderBottom: '1px solid #f0f0f0',
            marginBottom: 24,
          }}>
            <div style={{ 
              fontSize: 16, 
              color: '#666',
              lineHeight: 1.6,
              textAlign: 'center',
            }}>
              {questionnaireData.description}
            </div>
          </div>
        )}

        {/* 问题列表 */}
        {questions.length > 0 ? (
          <QuestionListForm
            questions={questions}
            form={form}
            onValuesChange={handleValuesChange}
          />
        ) : (
          <div style={{ 
            textAlign: 'center', 
            padding: '60px 20px',
            color: '#999',
          }}>
            <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂无问题</div>
          </div>
        )}

        {/* 提交按钮 */}
        {questions.length > 0 && (
          <div style={{ 
            textAlign: 'center', 
            marginTop: 40,
            paddingTop: 24,
            borderTop: '1px solid #f0f0f0',
          }}>
            <Space size="large">
              <Button size="large" onClick={handleBack}>
                取消
              </Button>
              <Button 
                type="primary" 
                size="large"
                icon={<SendOutlined />}
                loading={submitting}
                onClick={handleSubmit}
              >
                提交问卷
              </Button>
            </Space>
          </div>
        )}
      </Card>
    </PageContainer>
  );
};

export default QuestionFill;
