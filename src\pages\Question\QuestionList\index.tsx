import React, { useState, useRef, useEffect } from 'react';
import { ExclamationCircleOutlined, PlusOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { message, Dropdown, Menu, Button, Select, Modal, Tag, Popconfirm } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { useModel, history } from '@umijs/max';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { 
  queryQuestionnaireList, 
  publishQuestionnaire, 
  deleteQuestionnaire, 
  queryListById 
} from '@/services/api/question';
import PreveButton from './components/PreveButton';

const { Option } = Select;

export type ListType = {
  value?: any;
  label?: string;
};

const QuestionList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const actionRef = useRef<ActionType>();
  const [selectedRows, setSelectedRows] = useState<QUESTION.QuestionnaireListItem[]>([]);
  const { hosList, fetchHosList } = useModel('hospital');

  useEffect(() => {
    fetchHosList();
  }, []);

  /**
   * 查询问卷详情
   * @param fields
   */
  const selectById = async (fields: { id: number }) => {
    const hide = message.loading('正在查询');
    try {
      const response = await queryListById({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('查询成功');
        return response;
      }
      message.error(response.msg || '查询失败');
      return false;
    } catch (error) {
      hide();
      message.error('查询失败请重试！');
      return false;
    }
  };

  /**
   * 发布/暂停问卷
   * @param fields
   */
  const handlePublish = async (fields: { id: number; status: number }) => {
    const hide = message.loading(fields.status === 1 ? '正在发布' : '正在暂停');
    try {
      const response = await publishQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success(fields.status === 1 ? '发布成功' : '暂停成功');
        actionRef.current?.reload?.();
        return true;
      }
      message.error(response.msg || '操作失败');
      return false;
    } catch (error) {
      hide();
      message.error('操作失败请重试！');
      return false;
    }
  };

  /**
   * 删除问卷
   * @param fields
   */
  const handleDelete = async (fields: { id: number }) => {
    const hide = message.loading('正在删除');
    try {
      const response = await deleteQuestionnaire({ ...fields });
      if (response.code === '0') {
        hide();
        message.success('删除成功');
        actionRef.current?.reload?.();
        return true;
      }
      message.error(response.msg || '删除失败');
      return false;
    } catch (error) {
      hide();
      message.error('删除失败请重试！');
      return false;
    }
  };

  /**
   * 预览问卷
   * @param record
   */
  const handlePreview = async (record: QUESTION.QuestionnaireListItem) => {
    if (!record.id) return;

    const result = await selectById({ id: record.id });
    if (result && result.data) {
      // 通过路由状态传递问卷基本信息
      history.push(`/question/list/${record.id}?mode=preview`, {
        questionnaire: {
          id: record.id,
          title: record.title,
          description: record.description,
          status: record.status,
          createTime: record.createTime,
          creator: record.creator,
        }
      });
    }
  };

  /**
   * 编辑问卷
   * @param record
   */
  const handleEdit = (record: QUESTION.QuestionnaireListItem) => {
    // 通过路由状态传递问卷基本信息
    history.push(`/question/list/${record.id}?mode=edit`, {
      questionnaire: {
        id: record.id,
        title: record.title,
        description: record.description,
        status: record.status,
        createTime: record.createTime,
        creator: record.creator,
      }
    });
  };

  /**
   * 新增问卷
   */
  const handleAdd = () => {
    history.push('/question/list/new?mode=add');
  };

  const columns: ProColumns<QUESTION.QuestionnaireListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '问卷标题',
      dataIndex: 'title',
      ellipsis: true,
      width: 200,
    },
    {
      title: '问卷描述',
      dataIndex: 'description',
      hideInSearch: true,
      ellipsis: true,
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      width: 100,
      render: (_, record) => {
        let color = 'default';
        let text = '草稿';
        if (record.status === 1) {
          color = 'success';
          text = '已发布';
        } else if (record.status === 2) {
          color = 'warning';
          text = '已暂停';
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInTable: true,
      valueEnum: {
        0: { text: '草稿' },
        1: { text: '已发布' },
        2: { text: '已暂停' },
      },
    },
    {
      title: '机构名称',
      dataIndex: 'saID',
      renderText: (_, record) => record.hospitalName,
      renderFormItem: (item, { type, defaultRender, ...rest }) => {
        const options = hosList.map((c: ListType) => ({
          value: c.value,
          label: c.label,
        }));
        return (
          <Select placeholder="请选择" showSearch optionFilterProp="label" options={options} allowClear />
        );
      },
      colSize: 2,
    },
    {
      title: '问题数量',
      dataIndex: 'questionCount',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '答卷数量',
      dataIndex: 'answerCount',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => ({
          beginTime: value[0],
          endTime: value[1],
        }),
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (_, record) => [
        <a key="preview" onClick={() => handlePreview(record)}>
          预览
        </a>,
        <a key="edit" onClick={() => handleEdit(record)}>
          编辑
        </a>,
        record.status === 0 && (
          <a
            key="publish"
            onClick={() => handlePublish({ id: record.id!, status: 1 })}
          >
            发布
          </a>
        ),
        record.status === 1 && (
          <a
            key="pause"
            onClick={() => handlePublish({ id: record.id!, status: 2 })}
          >
            暂停
          </a>
        ),
        record.status === 2 && (
          <a
            key="republish"
            onClick={() => handlePublish({ id: record.id!, status: 1 })}
          >
            重新发布
          </a>
        ),
        <Popconfirm
          key="delete"
          title="确定要删除这个问卷吗？"
          onConfirm={() => handleDelete({ id: record.id! })}
          okText="确定"
          cancelText="取消"
        >
          <a style={{ color: 'red' }}>删除</a>
        </Popconfirm>,
      ].filter(Boolean),
    },
  ];

  return (
    <PageContainer header={{ breadcrumb: {} }}>
      <ProTable<QUESTION.QuestionnaireListItem>
        headerTitle="问卷配置"
        actionRef={actionRef}
        rowKey="id"
        scroll={{ x: 1500 }}
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
        }}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={handleAdd}
          >
            <PlusOutlined /> 新增问卷
          </Button>,
        ]}
        request={queryQuestionnaireList}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      
      {selectedRows?.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <span>
            已选择 <a style={{ fontWeight: 600 }}>{selectedRows.length}</a> 项
            <Button
              style={{ marginLeft: 8 }}
              onClick={() => {
                setSelectedRows([]);
                actionRef.current?.clearSelected?.();
              }}
            >
              取消选择
            </Button>
          </span>
        </div>
      )}
    </PageContainer>
  );
};

export default QuestionList;
