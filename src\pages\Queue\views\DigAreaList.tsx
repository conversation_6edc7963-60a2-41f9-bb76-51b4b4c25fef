import { Tag } from 'antd';
import { ProList } from '@ant-design/pro-components';
import { getDigAreaDivisionalInfo } from '@/services/api/queue';
import { useRequest } from 'ahooks';
import styles from '../index.less'

    // 请求诊区运营总览
    const queryDigAreaDivisionalInfo = async(digAreaCode: string) =>{
        try {
            const response = await getDigAreaDivisionalInfo({
                digAreaCode
            });
            if (response.code === '0') {
                return formatDigAreaData(response.data)
            }
            return false;
        } catch(error){
            return false;
        }
    }

    // 处理诊区总览列表
    const formatDigAreaData = (data: any) => {
        return data.map((item: any) => ({
            title: <div className={styles.digDataName}>{item.deptName}</div>,
            actions: [],
            content: (
                <div style={{ display: 'flex', justifyContent: "space-between", width: '100%'}}>
                    <div style={{ width: '80px', paddingLeft: 10}}>
                        <div>等候：<span className={styles.num}>{item.waitingNum}</span></div>
                        <div>过号：<span className={styles.num}>{item.haveNoNum}</span></div>
                        <div>已叫：<span className={styles.num}>{item.overNum}</span></div>
                    </div>
                    <div style={{flex: 1}}>
                        <div className={styles.callingInfo}>当前呼叫</div>
                        <div className={styles.callingPatient}><Tag color="green">{item.callingInfo?.patientName}{item.callingInfo?.patientName?`（${item.callingInfo?.noon === 1 ? '上午': item.callingInfo?.noon === 2? '下午':'全天'}${item.callingInfo?.checkCode}号）`: ''}</Tag></div>
                        <div className={styles.callingDoctor}>医生：<span>{item.doctorName}</span></div>
                    </div>
                </div>
            ),
            params: item
        }))
  }
  
  const DigAreaList: React.FC<{code: string, handleMenuSelect: Function , setMenuListSelect: Function}> = ({
    code,
    handleMenuSelect,
    setMenuListSelect,
  }) => {
    const { data, loading } = useRequest(() => queryDigAreaDivisionalInfo(code), {
        refreshDeps: [code],
        pollingInterval: 5000,
        pollingWhenHidden: true
    });
    return (
      <div style={{
        margin: '0 -24px',
      }}>
        <ProList
            loading={loading}
            pagination={false}
            showActions="hover"
            rowSelection={{}}
            grid={{ gutter: 8, column: 4 }}
            onItem={({ params }: any) => {
                return {
                    onClick: () => {
                        setMenuListSelect(`${params.deptCode}|dept|${params.deptName}|${params.deptID}|HASDOC`)
                        handleMenuSelect({key: `${params.deptCode}|dept|${params.deptName}|${params.deptID}|HASDOC`})
                    }
                };
            }}
            metas={{
                title: {},
                subTitle: {},
                type: {},
                content: {},
                actions: {},
            }}
            dataSource={data}
            bordered
        />
      </div>
    );
  };
  
  export default DigAreaList;