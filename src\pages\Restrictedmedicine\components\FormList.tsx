import { ProFormText, ProFormSelect } from '@ant-design/pro-form';

const FormList = () => {
  return (
    <>
      <ProFormText
        rules={[
          {
            required: true,
            message: '药品名称为必填项',
          },
        ]}
        name="ypmc"
        label="药品名称"
        placeholder="请输入诊断名称"
      />
      <ProFormSelect
        name="xzdj"
        label="药品级别"
        placeholder="请输入药品级别"
        options={[
          {value: '1', label: '非限制使用级'},
          {value: '2', label: '限制使用级'},
          {value: '3', label: '特殊使用级'},
        ]}
        rules={[
          {
            required: true,
            message: '药品级别为必填项',
          },
        ]}
      />
      <ProFormText
        name="gjz"
        label="药品别名"
        placeholder="请输入药品别名（多个可用英文逗号隔开填入）"
      />
    </>
  );
};

export default FormList;
