declare namespace RestrictedMedicine {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 诊断项目
    type ListItem = {        
        isTest?: boolean; // 是否测试数据
        saID?: string;      // 机构ID
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        ypmc?: string; // 药品名称
        xzdj?: string;  // 限制级别
        gjz?: string; // 关键字
        sort?: string;  // 诊断排序
        type?: string;     // 类型
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number;    // 状态
    };
    // 诊断列表
    type List = {
        /** 列表的内容 **/
        data?: DiagnosisListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}