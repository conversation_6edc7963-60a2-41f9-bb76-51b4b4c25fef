declare namespace USAGEINFO {
    type PageParams = {
        current?: number;
        pageSize?: number;
    };
    // 用法信息
    type DescribesListItem = {        
        isTest?: boolean; // 是否测试数据
        saID?: string;      // 机构ID
        apiVersion?: string;// 版本号
        patientID?: string;    // 患者ID
        deviceCode?: string;     // 设备号
        name?: string; // 名称
        code?: string;  // 代码
        pyCode?: string; //拼音搜索
        sort?: string;  // 序号
        type?: string;     // 类型
        id?: string; // ID
        createTime?: string; // 创建时间
        state?: number;    // 状态
    };
    // 用法信息列表
    type DescribesList = {
        /** 列表的内容 **/
        data?: DescribesListItem[];
        /** 列表的内容总数 **/
        total?: number;
        msg?: string;
        code?: number;
    };
}