@import (reference) '~antd/es/style/themes/index';

@vw: 19.2vw;
@vh: 10.8vh;

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  // background: @layout-body-background;
}

.lang {
  width: 100%;
  height: 40px;
  line-height: 44px;
  text-align: right;
  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  flex: 1;
  padding: 0 0;
}

.block {
  width: 45%;
  height: 760px;
}

@media (min-width: @screen-md-min) {
  .container {
    // background-image: url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr');
    // background-size: cover;
    // background-image: url('../../../assets/login.jpg');
    // background-repeat: no-repeat;
    // background-position: 100% 100%;
    // background-size: 100% 100%;
  }

  .content {
    padding: 0px 0 24px;
  }
}

.icon {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @primary-color;
  }
}

.top {
  text-align: center;
}

.header {
  height: 44px;
  line-height: 44px;
  a {
    text-decoration: none;
  }
}

.logo {
  height: 44px;
  margin-right: 16px;
  vertical-align: top;
}

.title {
  position: relative;
  top: 2px;
  color: @heading-color;
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
}


.loginBox{
  position: absolute;
  top: 153px;
  right: 0;
  width: 858px;
  // background-color: pink;
  // .desc {
  //   margin-top: 12px;
  //   margin-bottom: 40px;
  //   color: @text-color-secondary;
  //   font-size: @font-size-base;
  // }

  .desc {
    margin-bottom: 84/@vw;
    color: #3A6EE8;
    font-weight: 600;
    font-size: 50/@vw;
    line-height: 77/@vw;
    letter-spacing: 1px;
    text-align: center;
  }
  .subTitle{
    margin-bottom: 42/@vh;
    color: #000;
    font-weight: 600;
    font-size: 40/@vw;
    line-height: 58/@vw;
    text-align: center;
  }
}
