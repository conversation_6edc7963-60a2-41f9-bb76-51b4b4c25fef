import Footer from '@/components/Footer';
import { login } from '@/services/api/api';
import {
  LockOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  LoginForm,
  ProFormText,
} from '@ant-design/pro-components';
import MD5 from 'js-md5';
import { history, useModel } from '@umijs/max';
import { Alert, message, Modal} from 'antd';
import React, { useState } from 'react';
import { flushSync } from 'react-dom';
import defaultSettings from '../../../../config/defaultSettings';
import styles from './index.less';
import logoIcon from '../../../assets/logo-common.png'
import bgImage from '../../../assets/block.png';
const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => {
  return (
    <Alert
      style={{
        marginBottom: 24,
      }}
      message={content}
      type="error"
      showIcon
    />
  );
};
const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.LoginResult>({});
  const { initialState, setInitialState } = useModel('@@initialState');

  const { platfromType } = defaultSettings

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    console.log(userInfo)
    if (userInfo) {
      flushSync(() => {
        setInitialState((s: any) => ({
          ...s,
          currentUser: userInfo,
          menuData: userInfo.permissionsList
        }));
      });
    }
    return userInfo
  };

  const goPage = (permissionsList: any) => {
    const urlParams = new URL(window.location.href).searchParams;
    const redirect = urlParams.get('redirect') 
    let startPage = '';
    if(permissionsList[0]?.routes.length > 0){
      startPage = permissionsList[0].routes[0].path
    }else{
      startPage = permissionsList[0].path
    }
    permissionsList.forEach( (item: any) => {
      if(item.routes.findIndex( (i: any) => i.path === redirect) > -1){
        startPage = redirect
        return
      }
      if(item.path === redirect){
        startPage = redirect
        return
      }
    })
    
    history.push( startPage );
  }

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      const msg = await login({
        userName: values.userName,
        userPwd: MD5(values.userPwd)
      });
      if (msg.code === '0') {
        const defaultLoginSuccessMessage = '登录成功！';
        message.success(defaultLoginSuccessMessage);
        const res = await fetchUserInfo();
        goPage(res?.permissionsList)
        return;
      }
      console.log(msg);
      // 如果失败去设置用户错误信息
      setUserLoginState(msg);
    } catch (error) {
      const defaultLoginFailureMessage = '登录失败，请重试！';
      console.log(error);
      message.error(defaultLoginFailureMessage);
    }
  };
  const { code, msg} = userLoginState;
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.block}>
          <img src={bgImage} alt="bg"/>
        </div>
        <div className={styles.loginBox}>
          <LoginForm
            logo={<img alt="logo" src={logoIcon} />}
            title={platfromType === 'PlatformForDrugstore' ? "三医联动医保外配处方流转系统" : "智慧医疗服务管理平台"}
            subTitle={platfromType === 'PlatformForDrugstore' ? '设备管理 | 问诊查询 | 信息维护': platfromType === 'Queue'? '排队叫号管理平台': '设备管理 | 运维监控 | 信息维护'}
            onFinish={async (values) => {
              await handleSubmit(values as API.LoginParams);
            }}
          >
            {code === '2' && (
              <LoginMessage content={msg??'登录失败'} />
            )}
            <>
              <ProFormText
                name="userName"
                fieldProps={{
                  size: 'large',
                  prefix: <UserOutlined className={styles.prefixIcon} />,
                }}
                placeholder={'请输入用户名'}
                rules={[
                  {
                    required: true,
                    message: '用户名是必填项！',
                  },
                ]}
              />
              <ProFormText.Password
                name="userPwd"
                fieldProps={{
                  size: 'large',
                  prefix: <LockOutlined className={styles.prefixIcon} />,
                }}
                placeholder={'请输入密码'}
                rules={[
                  {
                    required: true,
                    message: '密码是必填项！',
                  },
                ]}
              />
            </>
            <div
              style={{
                paddingBottom: 24,
              }}
            >
              <a
                style={{
                  float: 'right',
                }}
                onClick={()=>{
                  Modal.info({
                    title: '请联系京威盛管理人员重置密码，联系方式：4008-678-511',
                  });
                }}
              >
                忘记密码 ?
              </a>
            </div>
          </LoginForm>
        </div>
      </div>
      <Footer />
    </div>
  );
};
export default Login;
