import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

/***************** 机构管理 *****************/
// 查询机构
export async function queryHosList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/hospital/getAreaList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  })
}

// 添加机构
export async function addHos(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/hospital/addArea.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 更新机构
export async function updateHos(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/hospital/updateArea.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 科室管理 *****************/
// 获取科室列表
export async function queryDeptList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/dept/getDeptList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增科室
export async function addDeptInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/dept/addDeptInfo.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改科室
export async function updateDeptInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/dept/updateDeptInfo.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 获取科室信息
export async function getDeptInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/dept/getDeptRoomInfo.sp`, {params});
}

/***************** 诊区管理 *****************/
// 查询诊区列表
export async function queryDiagAreaList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagArea/getDiagAreaList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 查询诊区列表不分页 用于关联列表
export async function queryDiagAreaListNoPage(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagArea/getDiagAreaListNoPage.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增诊区
export async function addDiagArea(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagArea/addDiagArea.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改诊区
export async function updateDiagArea(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagArea/updateDiagArea.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 诊区信息查询
export async function getDiagAreaInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagArea/getDiagAreaInfo.sp`, {params});
}


/***************** 诊室管理 *****************/
// 查询诊室列表
export async function queryClinicList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/deptroom/getDeptRoomList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增诊室
export async function addRoom(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/deptroom/addDeptRoom.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改诊室
export async function updateRoom(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/deptroom/updateDeptRoom.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 患者管理 *****************/
// 查询患者信息
export async function queryPatientList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/patient/getPatientList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 医生管理 *****************/
// 查询医生信息
export async function queryDocList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/getDoctorList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增医生
export async function addDoc(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/addDoctor.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改医生
export async function updateDoc(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/updateDoctor.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 获取医生信息
export async function getDoctorInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/getDoctorInfo.np`, {params});
}

// 医生签到诊室接口
export async function doctorCheckIn(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/addDoctorCheckRecord.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 医生签退诊室接口
export async function doctorCheckOut(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/updateDoctorCheckRecord.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 查询登录诊室的医生
export async function queryClinicDocList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/doctor/getDoctorCheckedList.sp`, {
    params: {
      ...params,
      timestamp: new Date().getTime()
    }
  });
}

// 排队叫号系统更新HIS医生信息
export async function freshDoctor(params?: Record<string, any>) {
  return request(`/${defaultSettings.queueApiName}/divisional/getDoctorInfo.np`, {
    params
  });
}

/**************** 人证核验数据 *********************/
export async function queryDataRecord(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/faceVer/jwsGetData.sp`, {
      method: 'POST',
      data: { ...params },
  });
}


/***************** 药品信息 *****************/
// 查询药品信息
export async function queryMedicineList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/getMedicine.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增药品
export async function addMedicine(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/addMedicine.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改药品
export async function updateMedicine(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/updateMedicine.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 开单项目 *****************/
// 查询开单项目信息
export async function queryPaymentList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/createPayment/getCreatePayment.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增开单项目
export async function addCreatePayment(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/createPayment/addCreatePayment.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改开单项目
export async function updateCreatePayment(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/createPayment/updateCreatePayment.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 诊断信息 *****************/
// 查询诊断信息
export async function queryDiagnosisList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagnosis/getDiagnosis.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增诊断信息
export async function addDiagnosis(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagnosis/addDiagnosis.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改诊断信息
export async function updateDiagnosis(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/diagnosis/updateDiagnosis.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}


/***************** 病情描述 *****************/
// 查询病情描述
export async function queryDescribesList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/describes/getDescribes.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增病情描述
export async function addDescribes(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/describes/addDescribes.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改病情描述
export async function updateDescribes(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/describes/updateDescribes.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 用法信息 *****************/
// 查询用法信息
export async function queryUsagesList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/usages/getUsages.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增用法信息
export async function addUsages(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/usages/addUsages.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改用法信息
export async function updateUsages(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/usages/updateUsages.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 处方信息 *****************/
// 查询处方信息列表
export async function queryPrescriptionList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/prescription/getPrescriptionList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 处方信息详情
export async function queryPrescriptionInfo(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/prescription/getPrescriptionInfo.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 处方信息更新
export async function updatePrescription(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/prescription/updatePrescription.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 频次信息 *****************/
// 查询频次信息
export async function queryFrequencyList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/frequency/getFrequency.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增频次信息
export async function addFrequency(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/frequency/addFrequency.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改频次信息
export async function updateFrequency(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/frequency/updateFrequency.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 限制药品 *****************/
// 查询限制药品
export async function queryRestrictedMedicineList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/restrictedmedicine/getRestrictedMedicineList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增限制药品
export async function insertRestrictedMedicine(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/restrictedmedicine/insertRestrictedMedicine.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 更新限制药品
export async function updateRestrictedMedicine(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/restrictedmedicine/updateRestrictedMedicine.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 药品关联诊断 *****************/
// 查询药品关联诊断
export async function getMedicineDiagnosisCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/getMedicineDiagnosisCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增药品关联诊断
export async function addMedicineDiagnosisCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/addMedicineDiagnosisCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 更新药品关联诊断
export async function updateMedicineDiagnosisCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/updateMedicineDiagnosisCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 药品关联病情 *****************/
// 查询药品关联诊断
export async function getMedicineDescribesCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/getMedicineDescribesCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增药品关联诊断
export async function addMedicineDescribesCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/addMedicineDescribesCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 更新药品关联诊断
export async function updateMedicineDescribesCorrelation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicinecorrelation/updateMedicineDescribesCorrelation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 一键导入药品关联
export async function updateCorrelationUsecount(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/consultation/updateCorrelationUsecount.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/***************** 机构药品关联 *****************/

// 查询机构药品库关联
export async function getMedicineSystemAssociationList(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/getMedicineSystemAssociationList.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 新增机构药品库关联
export async function addMedicineSystemAssociation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/addMedicineSystemAssociation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

// 修改机构药品库关联
export async function updateMedicineSystemAssociation(params?: Record<string, any>) {
  return request(`/${defaultSettings.apiName}/medicine/updateMedicineSystemAssociation.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}