import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

// 获取问卷列表接口
export async function queryQuestionnaireList(params) {
  return request(`/${defaultSettings.apiName}/question/getQuestionnaireList.np`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 新增问卷接口
export async function addQuestionnaire(params) {
  return request(`/${defaultSettings.apiName}/question/addQuestionnaire.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 修改问卷接口
export async function editQuestionnaire(params) {
  return request(`/${defaultSettings.apiName}/question/editQuestionnaire.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 发布暂停问卷接口
export async function publishQuestionnaire(params) {
  return request(`/${defaultSettings.apiName}/question/publishQuestionnaire.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 删除问卷接口
export async function deleteQuestionnaire(params) {
  return request(`/${defaultSettings.apiName}/question/deleteQuestionnaire.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 获取问卷问题列表接口
export async function queryListById(params) {
  return request(`/${defaultSettings.apiName}/question/getQuestionList.np`, {
    method: 'POST',
    params
  });
}

// 删除问题接口
export async function deleteQuestion(params) {
  return request(`/${defaultSettings.apiName}/question/deleteQuestion.sp`, {
    method: 'POST',
    params
  });
}

// 查答卷记录
export async function queryAnswerRecord(params) {
  return request(`/${defaultSettings.apiName}/answer/getAnswerRecord.np`, {
    method: 'GET',
    params
  });
}

// 查答卷记录--三亚窗口
export async function queryAnswerRecordPage(params) {
  return request(`/${defaultSettings.apiName}/answer/getAnswerRecordPage.np`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 根据问卷记录查答题结果展示接口
export async function getAnswerDisplayByRecord(params) {
  return request(`/${defaultSettings.apiName}/answer/answerDisplayByRecord.np`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

// 获取问卷好评数量
export async function queryGoodValue(params) {
  return request(`/${defaultSettings.apiName}/statement/getGoodValue.dp`, {
    method: 'GET',
    params
  });
}


