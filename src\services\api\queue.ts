import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

/***************** 分诊叫号接口 *****************/
// 获取排队叫号项目中科室列表
export async function getDeptList(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDeptList.np`, {
        params: data
    })
}

// 查询诊区下科室-医生菜单
export async function getDigDeptDoctorList(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDigDeptDoctorList.np`, {
        params: data
    })
}


// 获取科室未签到患者列表
export async function getDeptUncheckedPatientList(params?: {[key: string]: any}) {
    // 处理IE11不刷新的bug
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDeptUnSignedPatientList.np`, {params: data})
}

// 获取科室等候中患者列表
export async function getDeptPatientList(params?: {[key: string]: any}) {
    // 处理IE11不刷新的bug
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDeptPatientList.np`, {params: data})
}

// 获取科室医生列表
export async function getDeptDoctorList(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDoctorList.np`, {
        params: data
    })
}

// 医生、科室获取患者列表
export async function getPatientCheckinList(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getPatientCheckinList.np`, {
        params: data
    })
}

// 特殊患者置顶接口
export async function movePatient(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/movePatient.np`, {
        params: data
    })
}

// 患者指定医生接口
export async function moveToDoctor(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/moveToDoctor.np`, {
        params: data
    })
}

// 正常签到/过号签到/回诊签到
export async function patientCheckIn(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/addPatientCheckRecord.np`, {
        params: data
    })
}

// 设置患者优先
export async function priorityPatient(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/priorityPatient.np`, {
        params: data
    })
}

// 更新患者状态
export async function updatePatientState(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/updatePatientState.np`, {
        params: data
    })
}

// 诊区分诊运营总览
export async function getDigAreaDivisionalInfo(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getDigAreaDivisionalInfo.np`, {
        params: data
    })
}

// 科室分诊运营总览
export async function getDeptDivisionalInfo(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/getDeptDivisionalInfo.np`, {
        params: data
    })
}

// 诊区当日就诊量排行
export async function divisionalCountByDoctor(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/divisionalCountByDoctor.np`, {
        params: data
    })
}

// 诊区当日就诊趋势图
export async function divisionalCountByHour(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/statement/divisionalCountByHour.np`, {
        params: data
    })
}

// 查询科室医生统计列表
export async function queryDeptDoctorList(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/getDeptDoctorList.np`, {
        params: data
    })
}

// 获取科室统计数据
export async function getDeptConsultationTotalData(params?: {[key: string]: any}) {
    return request(`/${defaultSettings.queueApiName}/statement/getDeptConsultationTotalData`, {
        method: 'POST',
        data: params,
    })
}

// 获取科室坐诊数据
export async function getDeptConsultationData(params?: {[key: string]: any}) {
    return request(`/${defaultSettings.queueApiName}/statement/getDeptConsultationData`, {
        method: 'POST',
        data: params,
    })
}

// 获取科室医生坐诊数据
export async function getDoctorConsultationData(params?: {[key: string]: any}) {
    return request(`/${defaultSettings.queueApiName}/statement/getDoctorConsultationData`, {
        method: 'POST',
        data: params,
    })
}

// 上一位/下一位
export async function patientUpAndDown(params?: {[key: string]: any}) {
    const data = {
      ...params,
      date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/upAndDownPatient.np`, {
      params: data
    })
}

// 医生签到诊室接口
export async function doctorCheckIn(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/divisional/addDoctorCheckRecord.sp`, {
        params: data
    });
  }
  
// 医生签退诊室接口
export async function doctorCheckOut(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/divisional/updateDoctorCheckRecord.np`, {
        params: data
    });
}

// 转诊接口
export async function patientDeptReferral(params?: {[key: string]: any}) {
    const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/patient/patientDeptReferral.np`, {
        params: data
    });
}


// 查询科室是否有医生登录
export async function getDeptLoginDoctor(params?: {[key: string]: any}) {
        const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/divisional/getDeptLoginDoctor`, {
        params: data
    });
}

// 查询诊区是否有医生登录 用于下午回诊签到科室无医生情况
export async function getDigDeptDocList(params?: {[key: string]: any}) {
        const data = {
        ...params,
        date: new Date().getTime()
    }
    return request(`/${defaultSettings.queueApiName}/divisional/getDigDeptDocList.np`, {
        params: data
    });
}