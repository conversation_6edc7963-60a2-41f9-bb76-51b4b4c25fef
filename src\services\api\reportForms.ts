import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

// 查询报表
export async function queryDayForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getDailyReport.dp`, {
        method: 'GET',
        params,
    })
}

// 查询报表
export async function queryDayFormsNew(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getDailyReportNew.dp`, {
        method: 'GET',
        params,
    })
}

// 异常订单报表查询接口
export async function getAbnormalReport(params: any) {
    return request(`/${defaultSettings.apiName}/statement/getAbnormalReport.dp`, {
        method: 'GET',
        params,
    })
}

// 查询自助机功能报表接口
export async function queryFuncForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/personTimeStatistics.dp`, {
        method: 'GET',
        params,
    })
}

// 西北妇幼 查询报表接口
export async function queryXBFYFuncForms(params: any) {
    return request(`/${defaultSettings.apiName}/statement/xbfypersonTimeStatistics.dp`, {
        method: 'GET',
        params,
    })
}

/****************** 外配处方报表 ******************/
// 总计报表
export async function getPrescriptionStatisticsList(params: any) {
    return request(`/${defaultSettings.apiName}/prescriptionStatistics/getPrescriptionStatisticsList.sp`, {
        method: 'GET',
        params,
        skipErrorHandler: true,
    })
}
// 药店统计报表
export async function getPrescriptionStatisticsListBySAID(params: any) {
    return request(`/${defaultSettings.apiName}/prescriptionStatistics/getPrescriptionStatisticsListBySAID.sp`, {
        method: 'GET',
        params,
        skipErrorHandler: true,
    })
}
// 医生统计信息
export async function getPrescriptionStatisticsListByDoctor(params: any) {
    return request(`/${defaultSettings.apiName}/prescriptionStatistics/getPrescriptionStatisticsListByPrescriptionDoctor.sp`, {
        method: 'GET',
        params,
        skipErrorHandler: true,
    })
}
// 药师统计信息
export async function getPrescriptionStatisticsListByPharmacist(params: any) {
    return request(`/${defaultSettings.apiName}/prescriptionStatistics/getPrescriptionStatisticsListByCheckPharmacist.sp`, {
        method: 'GET',
        params,
        skipErrorHandler: true,
    })
}
// 日统计报表
export async function getPrescriptionStatisticsListByCreateTimeDay(params: any) {
    return request(`/${defaultSettings.apiName}/prescriptionStatistics/getPrescriptionStatisticsListByCreateTimeDay.sp`, {
        method: 'GET',
        params,
        skipErrorHandler: true,
    })
}