// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
import defaultSettings from '../../../config/defaultSettings';

/** 获取当前的用户 POST /user/getUserInfo.sp */
export async function currentUser(options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/getUserInfo.sp`, {
    method: 'POST',
    ...(options || {}),
  });
}

// 暂时没有
/** 退出登录接口 POST /api/login/outLogin */
export async function outLogin(options?: {[key: string]: any}) {
  return request('/api/login/outLogin', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 登录接口 POST /user/login.np */
export async function login(body: API.LoginParams, options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/login.np`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 修改用户密码接口 POST /user/updateUserPwd.sp */
export async function updateUserPwd(body: API.LoginParams, options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/updateUserPwd.sp`, {
    method: 'POST',
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/notices */
export async function getNotices(options?: {[key: string]: any}) {
  return request('/api/notices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取规则列表 GET /api/rule */
export async function rule(params?: {[key: string]: any}, options?: {[key: string]: any}) {
  return request('/api/rule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 新建规则 PUT /api/rule */
export async function updateRule(options?: {[key: string]: any}) {
  return request('/api/rule', {
    method: 'PUT',
    ...(options || {}),
  });
}

/** 新建规则 POST /api/rule */
export async function addRule(options?: {[key: string]: any}) {
  return request('/api/rule', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 删除规则 DELETE /api/rule */
export async function removeRule(options?: {[key: string]: any}) {
  return request('/api/rule', {
    method: 'DELETE',
    ...(options || {}),
  });
}

/********************** 字典管理 **********************/

/** 获取字典列表 GET /system/getDictionary.np */
export async function queryDictionary(params: {[key: string]: any}) {
  let typeString;
  if (toString.call(params) === '[object Object]') {
    typeString = params.type || '';
  }
  return request(
    `/${defaultSettings.apiName}/system/getDictionary.np`, {params}
  );
}

/** 获取字典类型列表 POST /system/getDictionaryTypeList.sp */
export async function queryDictionaryTypeList(
  params?: {[key: string]: any},
  options?: {[key: string]: any},
) {
  return request(`/${defaultSettings.apiName}/system/getDictionaryTypeList.sp`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 获取字典列表 POST /system/getDictionaryList.sp */
export async function queryDictionaryList(
  params?: {[key: string]: any},
  options?: {[key: string]: any},
) {
  return request(`/${defaultSettings.apiName}/system/getDictionaryList.sp`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 添加字典 POST /system/addDictionary.sp */
export async function addDictionary(params?: {[key: string]: any}, options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/system/addDictionary.sp`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}

/** 修改字典 POST /system/updateDictionary.sp */
export async function updateDictionary(params?: {[key: string]: any}, options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/system/updateDictionary.sp`, {
    method: 'POST',
    data: params,
    ...(options || {}),
  });
}



/** 获取用户操作列表 POST /statement/getUserOperatingList.sp */
export async function getUserOperatingList(params?: {[key: string]: any}, options?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/statement/getUserOperatingList.sp`, {
    method: 'GET',
    params,
    ...(options || {}),
  });
}


/**************** 角色管理 *****************/
/** 获取角色列表 POST /user/getRoleList.sp */
export async function queryRoleList(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/getRoleList.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

/** 新增角色 POST /user/addRole.sp */
export async function addRole(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/addRole.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** 修改角色 POST /user/updateRole.sp */
export async function updateRole(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/updateRole.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/** 获取角色功能列表 POST /user/getRolePermission.sp */
export async function getRolePer(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/getRolePermission.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/**  修改角色功能 POST /user/updateRolePermission.sp */
export async function updateRolePer(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/updateRolePermission.sp`, {
    method: 'POST',
    data: {
      ...params
    },
  });
}

/**  查询系统类型功能菜单  角色管理使用 无分页 POST /user/getFunctionList.sp */
export async function queryMenuList(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/getFunctionList.sp`, {
      method: 'POST',
      data: { ...params },
  });
}

/**************** 用户管理 *****************/
/** 获取用户列表 POST /user/queryUserList.sp */
export async function queryUserList(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/getUserList.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

/** 新增用户 POST /user/queryUserList.sp */
export async function addUser(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/addUser.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

/** 修改用户 POST /user/queryUserList.sp */
export async function updateUser(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/user/updateUser.sp`, {
    method: 'POST',
    data: {
      ...params
    }
  });
}

/**************** 更新管理 *****************/
// 版本文件上传
export async function addVersionFile(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/system/fileUpload.np`, {
      method: 'POST',
      params,
  });
}

// 软件版本更新列表 POST system/getVersionCheckList.np ---
export async function queryVersionCheckList(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/system/getVersionCheckList.np`, {
      method: 'POST',
      data: { ...params },
  });
}

/**************** 系统管理 *****************/
// 更新注册码
export async function updateRegCode(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/project/updateRegCode.sp`, {
      method: 'GET',
      params,
  });
}

// 更新系统记录时间
export async function updateSysTime(params?: {[key: string]: any}) {
  return request(`/${defaultSettings.apiName}/project/updateSysTime.sp`, {
      method: 'GET',
      params,
  });
}


