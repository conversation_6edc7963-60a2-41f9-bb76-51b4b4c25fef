/**
 * 音频通知工具类
 * 用于播放系统提醒声音
 */

export interface AudioConfig {
  volume?: number;
  loop?: boolean;
  preload?: boolean;
}

export class AudioNotification {
  private audio: HTMLAudioElement | null = null;
  private config: AudioConfig;

  constructor(audioPath: string, config: AudioConfig = {}) {
    this.config = {
      volume: 0.8,
      loop: false,
      preload: true,
      ...config,
    };

    this.initAudio(audioPath);
  }

  /**
   * 初始化音频元素
   */
  private initAudio(audioPath: string): void {
    try {
      this.audio = new Audio(audioPath);
      this.audio.volume = this.config.volume || 0.8;
      this.audio.loop = this.config.loop || false;
      
      if (this.config.preload) {
        this.audio.preload = 'auto';
      }

      // 添加错误处理
      this.audio.onerror = (error) => {
        console.error('音频加载失败:', error);
      };

      this.audio.oncanplaythrough = () => {
        console.log('音频已准备就绪');
      };
    } catch (error) {
      console.error('创建音频元素失败:', error);
    }
  }

  /**
   * 播放音频
   */
  async play(): Promise<boolean> {
    if (!this.audio) {
      console.warn('音频元素未初始化');
      return false;
    }

    try {
      // 重置播放位置
      this.audio.currentTime = 0;
      
      // 播放音频
      await this.audio.play();
      return true;
    } catch (error) {
      console.warn('播放音频失败:', error);
      return false;
    }
  }

  /**
   * 停止播放
   */
  stop(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
    }
  }

  /**
   * 设置音量
   */
  setVolume(volume: number): void {
    if (this.audio && volume >= 0 && volume <= 1) {
      this.audio.volume = volume;
      this.config.volume = volume;
    }
  }

  /**
   * 获取音量
   */
  getVolume(): number {
    return this.audio ? this.audio.volume : 0;
  }

  /**
   * 设置循环播放
   */
  setLoop(loop: boolean): void {
    if (this.audio) {
      this.audio.loop = loop;
      this.config.loop = loop;
    }
  }

  /**
   * 销毁音频元素
   */
  destroy(): void {
    if (this.audio) {
      this.audio.pause();
      this.audio.src = '';
      this.audio = null;
    }
  }

  /**
   * 检查音频是否可用
   */
  isAvailable(): boolean {
    return this.audio !== null && !this.audio.error;
  }
}

/**
 * 创建默认的通知音频实例
 */
export const createNotificationAudio = (): AudioNotification => {
  return new AudioNotification('audio/avchat_ring.mp3', {
    volume: 0.6,
    loop: false,
    preload: true,
  });
};

/**
 * 音频通知管理器
 * 管理多种类型的通知音频
 */
export class AudioNotificationManager {
  private audioMap: Map<string, AudioNotification> = new Map();

  /**
   * 注册音频
   */
  register(key: string, audioPath: string, config?: AudioConfig): void {
    const audio = new AudioNotification(audioPath, config);
    this.audioMap.set(key, audio);
  }

  /**
   * 播放指定音频
   */
  async play(key: string): Promise<boolean> {
    const audio = this.audioMap.get(key);
    if (audio) {
      return await audio.play();
    }
    console.warn(`音频 ${key} 未找到`);
    return false;
  }

  /**
   * 停止指定音频
   */
  stop(key: string): void {
    const audio = this.audioMap.get(key);
    if (audio) {
      audio.stop();
    }
  }

  /**
   * 停止所有音频
   */
  stopAll(): void {
    this.audioMap.forEach(audio => audio.stop());
  }

  /**
   * 设置指定音频的音量
   */
  setVolume(key: string, volume: number): void {
    const audio = this.audioMap.get(key);
    if (audio) {
      audio.setVolume(volume);
    }
  }

  /**
   * 销毁所有音频
   */
  destroy(): void {
    this.audioMap.forEach(audio => audio.destroy());
    this.audioMap.clear();
  }
}

/**
 * 默认音频管理器实例
 */
export const defaultAudioManager = new AudioNotificationManager();

// 注册默认音频
defaultAudioManager.register('notification', 'audio/avchat_connecting.mp3', { volume: 0.6 });
defaultAudioManager.register('warning', 'audio/avchat_no_response.mp3', { volume: 0.7 });
defaultAudioManager.register('error', 'audio/avchat_peer_reject.mp3', { volume: 0.8 });
