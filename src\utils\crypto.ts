import CryptoJS from 'crypto-js';
const KEY = CryptoJS.enc.Utf8.parse("10148522ABERT65Y");  //十六位十六进制数作为密钥 
const IV = CryptoJS.enc.Utf8.parse('afvfgey288215558');   //十六位十六进制数作为密钥偏移量 

//加密方法
function Encrypt(word) {
    const srcs = CryptoJS.enc.Utf8.parse(word);
    const encrypted = CryptoJS.AES.encrypt(srcs, KEY, {
        iv: IV, 
        mode: CryptoJS.mode.CBC, 
        padding: CryptoJS.pad.ZeroPadding 
    });
    return encrypted.ciphertext.toString().toUpperCase();
}

//解密方法
function Decrypt(word) {
    const encryptedHexStr = CryptoJS.enc.Hex.parse(word);
    const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    const decrypt = CryptoJS.AES.decrypt(srcs, KEY, { 
        iv: IV,
        mode: CryptoJS.mode.CBC, 
        padding: CryptoJS.pad.ZeroPadding 
    });
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
}

export default {
    Decrypt,
    Encrypt
}

