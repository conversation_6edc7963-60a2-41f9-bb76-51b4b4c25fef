import defaultSettings from '../../config/defaultSettings';
/**
 * WebSocket 工具类
 * 用于处方管理系统的实时消息通知
 */

export interface WebSocketMessage {
  type: 'new_prescription' | 'prescription_audit' | 'prescription_refund' | 'system_message' | 'auth';
  patientName?: string;
  prescriptionNo?: string;
  status?: string;
  amount?: number;
  message?: string;
  userId?: string;
  userType?: string;
}

export interface WebSocketConfig {
  url: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
}

export class PrescriptionWebSocket {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isManualClose = false;

  constructor(config: WebSocketConfig) {
    this.config = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      ...config,
    };
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.config.url);
        
        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          resolve();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          reject(error);
        };

        this.ws.onclose = (event) => {
          console.log('WebSocket连接已关闭:', event.code, event.reason);
          this.stopHeartbeat();
          
          if (!this.isManualClose && this.reconnectAttempts < (this.config.maxReconnectAttempts || 10)) {
            this.reconnect();
          }
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
        return false;
      }
    }
    console.warn('WebSocket未连接，无法发送消息');
    return false;
  }

  /**
   * 设置消息处理器
   */
  onMessage(handler: (message: WebSocketMessage) => void): void {
    if (this.ws) {
      this.ws.onmessage = (event) => {
        console.log('收到消息', event)
        try {
          const data = JSON.parse(event.data);
          handler(data);
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
    }
  }

  /**
   * 关闭连接
   */
  close(): void {
    this.isManualClose = true;
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 获取连接状态
   */
  getReadyState(): number {
    return this.ws ? this.ws.readyState : WebSocket.CLOSED;
  }

  /**
   * 重连
   */
  private reconnect(): void {
    if (this.reconnectAttempts >= (this.config.maxReconnectAttempts || 10)) {
      console.error('WebSocket重连次数已达上限');
      return;
    }

    this.reconnectAttempts++;
    console.log(`WebSocket重连中... (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('WebSocket重连失败:', error);
      });
    }, this.config.reconnectInterval);
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'system_message', message: 'ping' });
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

/**
 * 获取WebSocket服务器地址
 * @param userId 用户ID，可选参数
 */
export const getWebSocketUrl = (userId?: string | number): string => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = process.env.NODE_ENV === 'development'
    ? '**************:9999'
    : window.location.host;

  // 构建基础URL
  const baseUrl = `${protocol}//${host}/${defaultSettings.apiName}/websocket`;

  // 如果提供了userId，添加查询参数
  if (userId) {
    return `${baseUrl}?userID=${userId}`;
  }

  return baseUrl;
};
